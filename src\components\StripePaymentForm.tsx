import { useState, FormEvent } from 'react';
import { CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { useCart } from '../context/CartContext';
import LoadingSpinner from './LoadingSpinner';

interface StripePaymentFormProps {
  onSuccess: () => void;
  onError: (error: string) => void;
  clientSecret: string;
  formData: any;
}

export function StripePaymentForm({ onSuccess, onError, clientSecret, formData }: StripePaymentFormProps) {
  const stripe = useStripe();
  const elements = useElements();
  const [isProcessing, setIsProcessing] = useState(false);
  const [cardError, setCardError] = useState('');
  const { totalPrice } = useCart();

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    event.stopPropagation(); // Stop event propagation to prevent parent form submission

    if (!stripe || !elements) {
      // Stripe.js has not loaded yet
      return;
    }

    setIsProcessing(true);
    setCardError('');

    const cardElement = elements.getElement(CardElement);

    if (!cardElement) {
      setIsProcessing(false);
      setCardError('Card element not found');
      return;
    }

    // Create payment method
    const { error, paymentMethod } = await stripe.createPaymentMethod({
      type: 'card',
      card: cardElement,
      billing_details: {
        name: `${formData.firstName} ${formData.lastName}`,
        email: formData.email,
        address: {
          line1: formData.address,
          city: formData.city,
          state: formData.state,
          postal_code: formData.zipCode,
          country: formData.country,
        },
      },
    });

    if (error) {
      setCardError(error.message || 'An error occurred with your payment');
      setIsProcessing(false);
      onError(error.message || 'An error occurred with your payment');
      return;
    }

    // Confirm the payment with the client secret
    const { error: confirmError } = await stripe.confirmCardPayment(clientSecret, {
      payment_method: paymentMethod.id,
    });

    if (confirmError) {
      setCardError(confirmError.message || 'An error occurred with your payment');
      setIsProcessing(false);
      onError(confirmError.message || 'An error occurred with your payment');
      return;
    }

    // Payment succeeded
    setIsProcessing(false);
    onSuccess();
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="bg-white rounded-3xl shadow-lg border border-gray-100 p-8">
        <div className="flex items-center space-x-3 mb-8">
          <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
              <line x1="1" y1="10" x2="23" y2="10"></line>
            </svg>
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Payment Information</h2>
            <p className="text-sm text-gray-600">Enter your card details securely</p>
          </div>
        </div>

        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Card Details
          </label>
          <div className="p-4 border border-gray-300 rounded-xl focus-within:ring-2 focus-within:ring-green-500 focus-within:border-transparent transition-colors">
            <CardElement
              options={{
                style: {
                  base: {
                    fontSize: '16px',
                    color: '#424770',
                    '::placeholder': {
                      color: '#aab7c4',
                    },
                  },
                  invalid: {
                    color: '#9e2146',
                  },
                },
              }}
            />
          </div>
          {cardError && (
            <p className="mt-2 text-sm text-red-600">{cardError}</p>
          )}
        </div>

        <div className="mt-6">
          <button
            type="submit"
            disabled={!stripe || isProcessing}
            className={`w-full px-6 py-3 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-xl font-semibold shadow-lg hover:from-green-700 hover:to-green-800 transition-all duration-300 ${isProcessing ? 'opacity-70 cursor-not-allowed' : 'hover:shadow-xl transform hover:scale-105'}`}
          >
            {isProcessing ? (
              <div className="flex items-center justify-center space-x-2">
                <LoadingSpinner size="small" color="text-white" />
                <span>Processing...</span>
              </div>
            ) : (
              `Pay $${(totalPrice * 1.08).toFixed(2)}`
            )}
          </button>
        </div>

        <div className="mt-4 flex items-center justify-center space-x-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
            <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
          </svg>
          <span className="text-sm text-gray-600">Secure payment processing</span>
        </div>
      </div>
    </form>
  );
}