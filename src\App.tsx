import React, { useState } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from 'react-router-dom';
import { CartProvider } from './context/CartContext';
import { WishlistProvider } from './context/WishlistContext';
import { StripeProvider } from './context/StripeContext';
import { ToastProvider } from './context/ToastContext';
import { AuthProvider, useAuth } from './context/AuthContext';
import { Header } from './components/Header';
import { Footer } from './components/Footer';
import { Home } from './pages/Home';
import { Shop } from './pages/Shop';
import { About } from './pages/About';
import { Contact } from './pages/Contact';
import { CartPage } from './pages/CartPage';
import { Checkout } from './pages/Checkout';
import { Account } from './pages/Account';
import { Wishlist } from './pages/Wishlist';
import { Login } from './pages/Login';
import { Register } from './pages/Register';
import { NotFound } from './pages/NotFound';
import ErrorBoundary from './components/ErrorBoundary';

// Lazy load admin pages
const AdminDashboard = React.lazy(() => import('./pages/admin/Dashboard'));
const AdminProducts = React.lazy(() => import('./pages/admin/Products'));
const AdminOrders = React.lazy(() => import('./pages/admin/Orders'));
const AdminUsers = React.lazy(() => import('./pages/admin/Users'));

// Footer wrapper component to conditionally render footer based on route
const FooterWrapper = () => {
  const location = useLocation();
  const isAdminRoute = location.pathname.startsWith('/admin');
  
  // Don't render footer on admin routes
  if (isAdminRoute) {
    return null;
  }
  
  return <Footer />;
};

// Protected route component
const ProtectedRoute = ({ children, requireAdmin = false }: { children: React.ReactNode; requireAdmin?: boolean }) => {
  const { isAuthenticated, isAdmin, loading } = useAuth();
  
  if (loading) {
    return <div className="flex justify-center items-center h-screen">Loading...</div>;
  }
  
  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }
  
  if (requireAdmin && !isAdmin) {
    return <Navigate to="/account" />;
  }
  
  return children;
};



function App() {
  const [searchQuery, setSearchQuery] = useState('');
  
  // Suspense fallback for lazy-loaded components
  const adminFallback = (
    <div className="flex justify-center items-center h-screen">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900"></div>
    </div>
  );

  return (
    <ErrorBoundary>
      <ToastProvider>
        <WishlistProvider>
          <CartProvider>
          <Router>
            <AuthProvider>
              <StripeProvider>
                <div className="min-h-screen bg-gray-50">
                  <Header
                    searchQuery={searchQuery}
                    onSearch={setSearchQuery}
                  />

                  <Routes>
                    {/* Public routes */}
                    <Route path="/" element={<Home searchQuery={searchQuery} />} />
                    <Route path="/shop" element={<Shop searchQuery={searchQuery} />} />
                    <Route path="/about" element={<About />} />
                    <Route path="/contact" element={<Contact />} />
                    <Route path="/cart" element={<CartPage />} />
                    <Route path="/login" element={<Login />} />
                    <Route path="/register" element={<Register />} />
                    
                    {/* Protected user routes */}
                    <Route path="/checkout" element={
                      <ProtectedRoute>
                        <Checkout />
                      </ProtectedRoute>
                    } />
                    <Route path="/account" element={
                      <ProtectedRoute>
                        <Account />
                      </ProtectedRoute>
                    } />
                    <Route path="/wishlist" element={<Wishlist />} />
                    
                    {/* Admin routes */}
                    <Route path="/admin" element={
                      <ProtectedRoute requireAdmin={true}>
                        <React.Suspense fallback={adminFallback}>
                          <AdminDashboard />
                        </React.Suspense>
                      </ProtectedRoute>
                    } />
                    <Route path="/admin/products" element={
                      <ProtectedRoute requireAdmin={true}>
                        <React.Suspense fallback={adminFallback}>
                          <AdminProducts />
                        </React.Suspense>
                      </ProtectedRoute>
                    } />
                    <Route path="/admin/orders" element={
                      <ProtectedRoute requireAdmin={true}>
                        <React.Suspense fallback={adminFallback}>
                          <AdminOrders />
                        </React.Suspense>
                      </ProtectedRoute>
                    } />
                    <Route path="/admin/users" element={
                      <ProtectedRoute requireAdmin={true}>
                        <React.Suspense fallback={adminFallback}>
                          <AdminUsers />
                        </React.Suspense>
                      </ProtectedRoute>
                    } />
                    
                    {/* 404 route */}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                  {/* Footer is conditionally rendered in the FooterWrapper component */}
                  <FooterWrapper />
                </div>
              </StripeProvider>
            </AuthProvider>
          </Router>
          </CartProvider>
        </WishlistProvider>
      </ToastProvider>
    </ErrorBoundary>
  );
}

export default App;