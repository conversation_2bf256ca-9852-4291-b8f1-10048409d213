# React E-commerce Project with Stripe Integration

## Overview
This is a React-based e-commerce application with Stripe payment integration. The application allows users to browse products, add them to a cart, and complete the checkout process with Stripe payments.

## Features
- Product browsing and filtering
- Shopping cart functionality
- Multi-step checkout process
- Stripe payment integration
- Order confirmation

## Setup Instructions

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn
- Stripe account

### Installation
1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
   or
   ```
   yarn install
   ```

### Stripe Configuration
1. Create a Stripe account at [https://stripe.com](https://stripe.com) if you don't have one
2. Get your API keys from the Stripe Dashboard
3. Create a `.env` file based on the provided `.env.example`:
   ```
   # Vite environment variables (recommended)
   VITE_STRIPE_PUBLISHABLE_KEY=your_publishable_key_here
   VITE_STRIPE_SECRET_KEY=your_secret_key_here
   VITE_API_URL=http://localhost:4000
   
   # Legacy format (for backward compatibility)
   REACT_APP_STRIPE_PUBLISHABLE_KEY=your_publishable_key_here
   REACT_APP_STRIPE_SECRET_KEY=your_secret_key_here
   ```
   
   > **Note:** This project uses Vite, which requires environment variables to be prefixed with `VITE_` to be accessible in the client-side code. The legacy `REACT_APP_` prefix is supported for backward compatibility.

### Server Setup
1. Navigate to the server directory:
   ```
   cd server
   ```
2. Install server dependencies:
   ```
   npm install
   ```
3. Start the server:
   ```
   npm start
   ```
   The server will run on [http://localhost:4000](http://localhost:4000)

### Running the Application

#### Option 1: Start both server and client with one command
```
npm run dev:all
```
This will start both the server and client applications automatically.

#### Option 2: Start server and client separately
1. Make sure the server is running (see Server Setup)
2. In a new terminal, start the React development server:
   ```
   npm start
   ```
   or
   ```
   yarn start
   ```
3. Open [http://localhost:3000](http://localhost:3000) in your browser

## Payment Process
The application uses Stripe for payment processing. Here's how it works:

1. User adds products to cart
2. User proceeds to checkout
3. User fills shipping information
4. User enters payment details using Stripe Elements
5. Payment is processed through Stripe
6. Order confirmation is displayed

## Important Notes
- This implementation includes both client-side and server-side components for Stripe integration
- The server handles payment intents securely using Stripe's API
- The client uses Stripe Elements for secure card input
- In development mode, there's a fallback to mock payment intents if the server is not running

## Server Implementation
The project includes a basic Express server that:

1. Creates payment intents using the Stripe API
2. Securely handles the Stripe secret key
3. Provides a RESTful API endpoint for the client

## Environment Variables
This project uses environment variables for configuration. The environment variables are loaded using:

1. **Client-side**: Vite's built-in environment variable handling (prefixed with `VITE_`)
2. **Server-side**: dotenv package to load variables from the `.env` file

### Available Environment Variables

| Variable | Purpose | Used In |
|----------|---------|--------|
| `VITE_STRIPE_PUBLISHABLE_KEY` | Stripe publishable key for client-side | Client |
| `VITE_STRIPE_SECRET_KEY` | Stripe secret key for server-side | Server |
| `VITE_API_URL` | API URL for client-server communication | Client |

### Environment Utility

The project includes an environment utility (`src/utils/env.ts`) that provides consistent access to environment variables throughout the application. This utility handles fallbacks and provides type safety.

Example usage:
```typescript
import { STRIPE_PUBLISHABLE_KEY, IS_DEVELOPMENT } from '../utils/env';

// Use environment variables
const stripe = loadStripe(STRIPE_PUBLISHABLE_KEY);

if (IS_DEVELOPMENT) {
  console.log('Running in development mode');
}
```

## Future Enhancements
To make this implementation production-ready, consider adding:

1. Webhook handlers for Stripe events (payment succeeded, failed, etc.)
2. Database integration for storing orders and customer information
3. Authentication and authorization
4. Error logging and monitoring
5. Additional payment methods (Apple Pay, Google Pay, etc.)

## License
MIT