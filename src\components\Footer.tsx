import { Link } from 'react-router-dom';
import { Mail, Phone, MapPin, Facebook, Twitter, Instagram, Youtube } from 'lucide-react';

export function Footer() {
  return (
    <footer className="bg-green-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <img
                src="/logo.jpg"
                alt="jaisalgoonline"
                className="h-12 w-auto object-contain"
              />
              <span className="text-xl font-bold">jaisalgoonline</span>
            </div>
            <p className="text-green-200 leading-relaxed">
              Your trusted destination for premium products. We bring you
              quality items, modern essentials, and everything you need.
            </p>
            <div className="flex space-x-4">
              <div className="p-2 bg-green-700 rounded-lg hover:bg-green-600 cursor-pointer transition-colors">
                <Facebook className="h-5 w-5 text-white" />
              </div>
              <div className="p-2 bg-green-700 rounded-lg hover:bg-green-600 cursor-pointer transition-colors">
                <Twitter className="h-5 w-5 text-white" />
              </div>
              <div className="p-2 bg-green-700 rounded-lg hover:bg-green-600 cursor-pointer transition-colors">
                <Instagram className="h-5 w-5 text-white" />
              </div>
              <div className="p-2 bg-green-700 rounded-lg hover:bg-green-600 cursor-pointer transition-colors">
                <Youtube className="h-5 w-5 text-white" />
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-green-300">Quick Links</h3>
            <ul className="space-y-3">
              <li><Link to="/about" className="text-green-200 hover:text-white transition-colors">About Us</Link></li>
              <li><Link to="/shop" className="text-green-200 hover:text-white transition-colors">Products</Link></li>
              <li><Link to="/cart" className="text-green-200 hover:text-white transition-colors">Shopping Cart</Link></li>
              <li><Link to="/contact" className="text-green-200 hover:text-white transition-colors">Contact</Link></li>
            </ul>
          </div>

          {/* Customer Service */}
          <div>
            <h3 className="text-lg font-semibold mb-4 text-green-300">Customer Service</h3>
            <ul className="space-y-3">
              <li><Link to="/contact" className="text-green-200 hover:text-white transition-colors">Shipping Info</Link></li>
              <li><Link to="/contact" className="text-green-200 hover:text-white transition-colors">Returns</Link></li>
              <li><Link to="/contact" className="text-green-200 hover:text-white transition-colors">Size Guide</Link></li>
              <li><Link to="/contact" className="text-green-200 hover:text-white transition-colors">FAQ</Link></li>
              <li><Link to="/contact" className="text-green-200 hover:text-white transition-colors">Support</Link></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-green-400" />
                <span className="text-gray-400"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-green-400" />
                <span className="text-gray-400">+****************</span>
              </div>
              <div className="flex items-center space-x-3">
                <MapPin className="h-5 w-5 text-green-400" />
                <span className="text-gray-400">New York, NY, USA</span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © 2024 jaisalgoonline. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link to="/contact" className="text-gray-400 hover:text-white text-sm transition-colors">Privacy Policy</Link>
            <Link to="/contact" className="text-gray-400 hover:text-white text-sm transition-colors">Terms of Service</Link>
            <Link to="/contact" className="text-gray-400 hover:text-white text-sm transition-colors">Return Policy</Link>
          </div>
        </div>
      </div>
    </footer>
  );
}