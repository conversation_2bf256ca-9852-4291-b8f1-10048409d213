import { Link } from 'react-router-dom';
import { Mail, Phone, MapPin, Shield, HelpCircle } from 'lucide-react';

export function UserAccountFooter() {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-green-900 text-white mt-8 rounded-3xl shadow-lg overflow-hidden w-full md:ml-0">
      <div className="px-6 py-8">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="flex items-center">
            <img
              src="/logo.jpg"
              alt="jaisalgoonline"
              className="h-10 w-auto object-contain mr-3"
            />
            <div>
              <p className="font-semibold text-white">jaisalgoonline</p>
              <p className="text-xs text-gray-300">Your trusted shopping destination</p>
            </div>
          </div>
          
          <div className="flex flex-wrap justify-center gap-6">
            <Link to="/contact" className="text-sm text-gray-300 hover:text-white transition-colors flex items-center">
              <HelpCircle className="h-4 w-4 mr-1 text-green-400" />
              <span>Help Center</span>
            </Link>
            <Link to="/privacy-policy" className="text-sm text-gray-300 hover:text-white transition-colors flex items-center">
              <Shield className="h-4 w-4 mr-1 text-green-400" />
              <span>Privacy Policy</span>
            </Link>
            <Link to="/terms-of-service" className="text-sm text-gray-300 hover:text-white transition-colors">
              Terms of Service
            </Link>
          </div>
        </div>
        
        <div className="border-t border-gray-800 mt-4 pt-4 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-gray-400 mb-2 md:mb-0">
            © {currentYear} jaisalgoonline. All rights reserved.
          </p>
          <div className="flex space-x-4">
            <a href="#" className="text-gray-400 hover:text-white transition-colors">
              <Mail className="h-4 w-4 text-green-400" />
            </a>
            <a href="#" className="text-gray-400 hover:text-white transition-colors">
              <Phone className="h-4 w-4 text-green-400" />
            </a>
            <a href="#" className="text-gray-400 hover:text-white transition-colors">
              <MapPin className="h-4 w-4 text-green-400" />
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default UserAccountFooter;