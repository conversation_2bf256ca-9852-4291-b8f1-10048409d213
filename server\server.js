const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const dotenv = require('dotenv');
const path = require('path');
const connectDB = require('./config/db');

// Load environment variables
dotenv.config({ path: '../.env' });

// Initialize Stripe with the secret key
const stripeKey = process.env.VITE_STRIPE_SECRET_KEY || process.env.REACT_APP_STRIPE_SECRET_KEY;

// Ensure the key is available
if (!stripeKey) {
  console.error('Stripe secret key is missing. Please check your .env file.');
}

// Import routes
const paymentRoutes = require('./routes/payment');
const userRoutes = require('./routes/users');
const productRoutes = require('./routes/products');
const orderRoutes = require('./routes/orders');
const statsRoutes = require('./routes/stats');
const reviewRoutes = require('./routes/reviews');

const app = express();
const PORT = process.env.PORT || 4000;

// Middleware
app.use(cors());
app.use(bodyParser.json());

// Connect to MongoDB
connectDB();

// Set up uploads directory in public folder for static access
const publicDir = path.join(__dirname, '../public');
const uploadsDir = path.join(publicDir, 'uploads');
// Create uploads directory if it doesn't exist
const fs = require('fs');
if (!fs.existsSync(uploadsDir)) {
  fs.mkdirSync(uploadsDir, { recursive: true });
  console.log('Uploads directory created in public folder');
}
// Serve static files from public directory
app.use(express.static(publicDir));
// For backward compatibility, keep the /uploads route
app.use('/uploads', express.static(uploadsDir));

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.status(200).json({ status: 'ok' });
});

// Use routes
app.use('/api/payment', paymentRoutes);
app.use('/api/users', userRoutes);
app.use('/api/products', productRoutes);
app.use('/api/orders', orderRoutes);
app.use('/api/stats', statsRoutes);
app.use('/api/reviews', reviewRoutes);

// Stripe Dashboard redirect for test mode
app.get('/api/stripe-dashboard', (req, res) => {
  res.redirect('https://dashboard.stripe.com/test/payments');
});

// Serve static files in production
if (process.env.NODE_ENV === 'production') {
  app.use(express.static(path.join(__dirname, '../dist')));
  
  app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../dist/index.html'));
  });
}

// Start the server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});