import { useState, useCallback } from 'react';

type ValidationRules<T> = {
  [K in keyof T]?: {
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    pattern?: RegExp;
    custom?: (value: T[K], formValues: T) => boolean;
    errorMessage?: string;
  };
};

type ValidationErrors<T> = {
  [K in keyof T]?: string;
};

export function useFormValidation<T extends Record<string, any>>(
  initialValues: T,
  validationRules: ValidationRules<T>
) {
  const [values, setValues] = useState<T>(initialValues);
  const [errors, setErrors] = useState<ValidationErrors<T>>({});
  const [touched, setTouched] = useState<Record<keyof T, boolean>>({} as Record<keyof T, boolean>);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      setValues((prev) => ({ ...prev, [name]: value }));
      
      // Validate field on change if it's been touched
      if (touched[name as keyof T]) {
        validateField(name as keyof T, value);
      }
    },
    [touched]
  );

  const handleBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      setTouched((prev) => ({ ...prev, [name]: true }));
      validateField(name as keyof T, value);
    },
    []
  );

  const validateField = useCallback(
    (name: keyof T, value: any) => {
      const rules = validationRules[name];
      if (!rules) return;

      let error = '';

      if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
        error = rules.errorMessage || 'This field is required';
      } else if (rules.minLength && typeof value === 'string' && value.length < rules.minLength) {
        error = rules.errorMessage || `Minimum length is ${rules.minLength} characters`;
      } else if (rules.maxLength && typeof value === 'string' && value.length > rules.maxLength) {
        error = rules.errorMessage || `Maximum length is ${rules.maxLength} characters`;
      } else if (rules.pattern && typeof value === 'string' && !rules.pattern.test(value)) {
        error = rules.errorMessage || 'Invalid format';
      } else if (rules.custom && !rules.custom(value, values)) {
        error = rules.errorMessage || 'Invalid value';
      }

      setErrors((prev) => ({ ...prev, [name]: error }));
    },
    [validationRules, values]
  );

  const validateForm = useCallback(() => {
    const newErrors: ValidationErrors<T> = {};
    let isValid = true;

    // Mark all fields as touched
    const newTouched = Object.keys(values).reduce(
      (acc, key) => ({ ...acc, [key]: true }),
      {} as Record<keyof T, boolean>
    );
    setTouched(newTouched);

    // Validate all fields
    Object.keys(validationRules).forEach((key) => {
      const fieldName = key as keyof T;
      const value = values[fieldName];
      validateField(fieldName, value);
      
      const rules = validationRules[fieldName];
      if (!rules) return;

      let error = '';

      if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
        error = rules.errorMessage || 'This field is required';
      } else if (rules.minLength && typeof value === 'string' && value.length < rules.minLength) {
        error = rules.errorMessage || `Minimum length is ${rules.minLength} characters`;
      } else if (rules.maxLength && typeof value === 'string' && value.length > rules.maxLength) {
        error = rules.errorMessage || `Maximum length is ${rules.maxLength} characters`;
      } else if (rules.pattern && typeof value === 'string' && !rules.pattern.test(value)) {
        error = rules.errorMessage || 'Invalid format';
      } else if (rules.custom && !rules.custom(value, values)) {
        error = rules.errorMessage || 'Invalid value';
      }

      if (error) {
        newErrors[fieldName] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  }, [validateField, validationRules, values]);

  const resetForm = useCallback(() => {
    setValues(initialValues);
    setErrors({});
    setTouched({} as Record<keyof T, boolean>);
  }, [initialValues]);

  return {
    values,
    errors,
    touched,
    handleChange,
    handleBlur,
    validateForm,
    resetForm,
    setValues,
  };
}