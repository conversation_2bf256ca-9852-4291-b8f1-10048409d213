import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Users, ShoppingBag, DollarSign, Package, TrendingUp, AlertCircle } from 'lucide-react';
import AdminLayout from './components/AdminLayout';
import { API_URL } from '../../utils/env';

interface Stats {
  totalUsers: number;
  totalOrders: number;
  totalProducts: number;
  totalRevenue: number;
  dailyRevenue: Array<{ date: string; amount: number }>;
  monthlySales: Array<{ month: number; year: number; amount: number; orders: number }>;
  topSellingProducts: Array<{
    product: string;
    quantity: number;
    revenue: number;
    productDetails?: {
      name: string;
      image: string;
      price: number;
    };
  }>;
  topCategories: Array<{ category: string; sales: number }>;
  recentOrders: Array<any>;
  lowStockProducts: Array<any>;
}

const Dashboard: React.FC = () => {
  const { token } = useAuth();
  const [stats, setStats] = useState<Stats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeframe, setTimeframe] = useState<'daily' | 'monthly'>('daily');

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${API_URL}/api/stats`, {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to fetch statistics');
        }

        const data = await response.json();
        setStats(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [token]);

  const refreshStats = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_URL}/api/stats/refresh`, {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('Failed to refresh statistics');
      }

      const data = await response.json();
      setStats(data.stats);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Format data for charts
  const prepareChartData = () => {
    if (!stats) return [];

    if (timeframe === 'daily') {
      return stats.dailyRevenue
        .slice(0, 7)
        .map(item => ({
          name: new Date(item.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          revenue: item.amount,
        }))
        .reverse();
    } else {
      return stats.monthlySales
        .slice(0, 6)
        .map(item => ({
          name: new Date(item.year, item.month).toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          revenue: item.amount,
          orders: item.orders,
        }))
        .reverse();
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };

  if (loading && !stats) {
    return (
      <AdminLayout>
        <div className="flex flex-col justify-center items-center h-96">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-green-200 border-t-green-600 mb-4"></div>
          <p className="text-gray-600 font-medium">Loading dashboard data...</p>
        </div>
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <div className="bg-gradient-to-r from-red-50 to-pink-50 border-l-4 border-red-500 p-6 mb-6 rounded-xl shadow-lg">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-red-500 rounded-xl flex items-center justify-center mr-4">
              <AlertCircle className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-red-800">Error Loading Dashboard</h3>
              <p className="text-red-700 mt-1">{error}</p>
            </div>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-6 py-3 bg-red-600 text-white rounded-xl hover:bg-red-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            Try Again
          </button>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="mb-8 flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <span className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
              <span className="text-white text-lg">📊</span>
            </span>
            Dashboard Overview
          </h1>
          <p className="text-gray-600 mt-2">Welcome back! Here's what's happening with your store today.</p>
        </div>
        <button
          onClick={refreshStats}
          disabled={loading}
          className="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          {loading ? (
            <span className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></span>
          ) : (
            <TrendingUp className="h-5 w-5 mr-2" />
          )}
          Refresh Stats
        </button>
      </div>

      {stats && (
        <>
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
            <div className="bg-gradient-to-br from-white to-blue-50/50 rounded-2xl shadow-lg p-6 flex items-center border border-blue-100 hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 p-4 mr-4 shadow-lg">
                <Users className="h-7 w-7 text-white" />
              </div>
              <div>
                <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">Total Users</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">{stats.totalUsers}</p>
                <p className="text-xs text-blue-600 font-medium mt-1">+12% from last month</p>
              </div>
            </div>

            <div className="bg-gradient-to-br from-white to-green-50/50 rounded-2xl shadow-lg p-6 flex items-center border border-green-100 hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="rounded-2xl bg-gradient-to-br from-green-500 to-emerald-600 p-4 mr-4 shadow-lg">
                <ShoppingBag className="h-7 w-7 text-white" />
              </div>
              <div>
                <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">Total Orders</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">{stats.totalOrders}</p>
                <p className="text-xs text-green-600 font-medium mt-1">+8% from last month</p>
              </div>
            </div>

            <div className="bg-gradient-to-br from-white to-purple-50/50 rounded-2xl shadow-lg p-6 flex items-center border border-purple-100 hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="rounded-2xl bg-gradient-to-br from-purple-500 to-purple-600 p-4 mr-4 shadow-lg">
                <Package className="h-7 w-7 text-white" />
              </div>
              <div>
                <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">Total Products</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">{stats.totalProducts}</p>
                <p className="text-xs text-purple-600 font-medium mt-1">+3 new this week</p>
              </div>
            </div>

            <div className="bg-gradient-to-br from-white to-yellow-50/50 rounded-2xl shadow-lg p-6 flex items-center border border-yellow-100 hover:shadow-xl transition-all duration-300 hover:scale-105">
              <div className="rounded-2xl bg-gradient-to-br from-yellow-500 to-orange-500 p-4 mr-4 shadow-lg">
                <DollarSign className="h-7 w-7 text-white" />
              </div>
              <div>
                <p className="text-sm text-gray-600 font-semibold uppercase tracking-wide">Total Revenue</p>
                <p className="text-3xl font-bold text-gray-900 mt-1">{formatCurrency(stats.totalRevenue)}</p>
                <p className="text-xs text-yellow-600 font-medium mt-1">+15% from last month</p>
              </div>
            </div>
          </div>

          {/* Revenue Chart */}
          <div className="bg-gradient-to-br from-white to-green-50/30 rounded-2xl shadow-xl p-8 mb-10 border border-green-100">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 space-y-4 sm:space-y-0">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 flex items-center">
                  <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-3">
                    <TrendingUp className="h-5 w-5 text-white" />
                  </div>
                  Revenue Overview
                </h2>
                <p className="text-gray-600 mt-1">Track your sales performance over time</p>
              </div>
              <div className="flex space-x-2 bg-white rounded-xl p-1 shadow-md border border-green-200">
                <button
                  onClick={() => setTimeframe('daily')}
                  className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 ${timeframe === 'daily' ? 'bg-green-600 text-white shadow-md' : 'text-gray-600 hover:text-green-600 hover:bg-green-50'}`}
                >
                  Daily
                </button>
                <button
                  onClick={() => setTimeframe('monthly')}
                  className={`px-4 py-2 rounded-lg font-medium text-sm transition-all duration-200 ${timeframe === 'monthly' ? 'bg-green-600 text-white shadow-md' : 'text-gray-600 hover:text-green-600 hover:bg-green-50'}`}
                >
                  Monthly
                </button>
              </div>
            </div>

            <div className="h-96 bg-white rounded-xl p-4 shadow-inner border border-gray-100">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={prepareChartData()} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#e5e7eb" />
                  <XAxis dataKey="name" tick={{ fontSize: 12 }} stroke="#6b7280" />
                  <YAxis tickFormatter={(value: number) => `$${value}`} tick={{ fontSize: 12 }} stroke="#6b7280" />
                  <Tooltip
                    formatter={(value: number) => [`$${value}`, 'Revenue']}
                    contentStyle={{
                      backgroundColor: 'white',
                      border: '1px solid #d1fae5',
                      borderRadius: '12px',
                      boxShadow: '0 10px 25px rgba(0,0,0,0.1)'
                    }}
                  />
                  <Bar dataKey="revenue" fill="url(#greenGradient)" radius={[8, 8, 0, 0]} />
                  <defs>
                    <linearGradient id="greenGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="#10b981" />
                      <stop offset="100%" stopColor="#059669" />
                    </linearGradient>
                  </defs>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-10">
            {/* Top Selling Products */}
            <div className="bg-gradient-to-br from-white to-green-50/30 rounded-2xl shadow-xl p-8 border border-green-100">
              <div className="flex items-center mb-6">
                <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center mr-3">
                  <Package className="h-5 w-5 text-white" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">Top Selling Products</h2>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-green-200">
                  <thead className="bg-gradient-to-r from-green-50 to-emerald-50">
                    <tr>
                      <th className="px-6 py-4 text-left text-xs font-bold text-green-700 uppercase tracking-wider">Product</th>
                      <th className="px-6 py-4 text-left text-xs font-bold text-green-700 uppercase tracking-wider">Sold</th>
                      <th className="px-6 py-4 text-left text-xs font-bold text-green-700 uppercase tracking-wider">Revenue</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-green-100">
                    {stats.topSellingProducts.slice(0, 5).map((product, index) => (
                      <tr key={index} className="hover:bg-green-50/50 transition-colors duration-200">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            {product.productDetails?.image && (
                              <img
                                src={product.productDetails.image}
                                alt={product.productDetails.name}
                                className="h-12 w-12 rounded-xl object-cover mr-4 shadow-md border border-green-200"
                              />
                            )}
                            <div className="text-sm font-semibold text-gray-900">
                              {product.productDetails?.name || 'Product ID: ' + product.product}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            {product.quantity} units
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-bold">
                          {formatCurrency(product.revenue)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <div className="mt-6 text-right">
                <Link
                  to="/admin/products"
                  className="inline-flex items-center text-green-600 hover:text-green-700 text-sm font-semibold bg-green-50 hover:bg-green-100 px-4 py-2 rounded-xl transition-all duration-200"
                >
                  View all products →
                </Link>
              </div>
            </div>

            {/* Recent Orders */}
            <div className="bg-gradient-to-br from-white to-blue-50/30 rounded-2xl shadow-xl p-8 border border-blue-100">
              <div className="flex items-center mb-6">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mr-3">
                  <ShoppingBag className="h-5 w-5 text-white" />
                </div>
                <h2 className="text-xl font-bold text-gray-900">Recent Orders</h2>
              </div>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-blue-200">
                  <thead className="bg-gradient-to-r from-blue-50 to-indigo-50">
                    <tr>
                      <th className="px-6 py-4 text-left text-xs font-bold text-blue-700 uppercase tracking-wider">Order ID</th>
                      <th className="px-6 py-4 text-left text-xs font-bold text-blue-700 uppercase tracking-wider">Customer</th>
                      <th className="px-6 py-4 text-left text-xs font-bold text-blue-700 uppercase tracking-wider">Amount</th>
                      <th className="px-6 py-4 text-left text-xs font-bold text-blue-700 uppercase tracking-wider">Status</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-blue-100">
                    {stats.recentOrders.slice(0, 5).map((order) => (
                      <tr key={order._id}>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500">
                          #{order._id.substring(order._id.length - 6)}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="text-sm font-medium text-gray-900">
                            {order.user?.firstName} {order.user?.lastName}
                          </div>
                          <div className="text-xs text-gray-500">{order.user?.email}</div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-medium">
                          {formatCurrency(order.total)}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <span
                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              order.status === 'delivered'
                                ? 'bg-green-100 text-green-800'
                                : order.status === 'processing'
                                ? 'bg-yellow-100 text-yellow-800'
                                : order.status === 'cancelled'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-blue-100 text-blue-800'
                            }`}
                          >
                            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <div className="mt-4 text-right">
                <Link
                  to="/admin/orders"
                  className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
                >
                  View all orders →
                </Link>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Top Categories */}
            <div className="bg-white rounded-xl shadow-md p-6">
              <h2 className="text-lg font-bold text-gray-900 mb-4">Top Categories</h2>
              <div className="space-y-4">
                {stats.topCategories.slice(0, 5).map((category, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <span className="text-sm font-medium text-gray-900">{category.category}</span>
                    </div>
                    <div className="flex items-center">
                      <span className="text-sm font-medium text-gray-900">{formatCurrency(category.sales)}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Low Stock Products */}
            <div className="bg-white rounded-xl shadow-md p-6">
              <h2 className="text-lg font-bold text-gray-900 mb-4">Low Stock Products</h2>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200">
                    {stats.lowStockProducts.slice(0, 5).map((product) => (
                      <tr key={product._id}>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <div className="flex items-center">
                            <img
                              src={product.image}
                              alt={product.name}
                              className="h-10 w-10 rounded-md object-cover mr-3"
                            />
                            <div className="text-sm font-medium text-gray-900">{product.name}</div>
                          </div>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap">
                          <span
                            className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                              !product.inStock ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                            }`}
                          >
                            {product.inStock ? 'In stock' : 'Out of stock'}
                          </span>
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 font-medium">
                          {formatCurrency(product.price)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              <div className="mt-4 text-right">
                <Link
                  to="/admin/products"
                  className="text-indigo-600 hover:text-indigo-800 text-sm font-medium"
                >
                  Manage inventory →
                </Link>
              </div>
            </div>
          </div>
        </>
      )}
    </AdminLayout>
  );
};

export default Dashboard;