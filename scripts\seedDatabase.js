const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// We'll use direct MongoDB operations to avoid Mongoose buffering issues

// Connect to MongoDB and get direct database access
const connectDB = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI;
    console.log('Attempting to connect to MongoDB...');
    console.log('MongoDB URI found:', mongoUri ? 'Yes' : 'No');

    if (!mongoUri) {
      throw new Error('MONGODB_URI environment variable is not set. Please check your .env file.');
    }

    await mongoose.connect(mongoUri, {
      serverSelectionTimeoutMS: 60000,
      socketTimeoutMS: 60000,
      connectTimeoutMS: 60000,
      maxPoolSize: 5,
    });

    console.log('MongoDB Connected...');

    // Get direct database access to bypass Mongoose buffering
    const db = mongoose.connection.db;
    await db.admin().ping();
    console.log('Database ping successful...');

    return db;

  } catch (err) {
    console.error('Database connection error:', err.message);
    process.exit(1);
  }
};

// Admin user data
const adminUser = {
  firstName: 'Admin',
  lastName: 'User',
  email: '<EMAIL>',
  password: 'admin123456',
  role: 'admin'
};

// Products data with external image URLs
const products = [
  {
    name: 'Premium Wireless Headphones',
    price: 299.99,
    originalPrice: 399.99,
    description: 'Experience premium sound quality with our wireless headphones featuring noise cancellation, 30-hour battery life, and premium comfort design.',
    category: 'Electronics',
    image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: true,
    quantity: 50,
    rating: 4.8,
    reviews: 128
  },
  {
    name: 'Smart Fitness Watch',
    price: 199.99,
    description: 'Track your fitness goals with this advanced smartwatch featuring heart rate monitoring, GPS, and 7-day battery life.',
    category: 'Electronics',
    image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: true,
    quantity: 75,
    rating: 4.6,
    reviews: 89
  },
  {
    name: 'Professional Camera Lens',
    price: 899.99,
    originalPrice: 1199.99,
    description: 'Professional-grade camera lens with superior optics, perfect for portrait and landscape photography.',
    category: 'Photography',
    image: 'https://images.unsplash.com/photo-1606983340126-99ab4feaa64a?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: true,
    quantity: 25,
    rating: 4.9,
    reviews: 67
  },
  {
    name: 'Gaming Mechanical Keyboard',
    price: 149.99,
    originalPrice: 199.99,
    description: 'High-performance mechanical keyboard with RGB backlighting and tactile switches for gaming enthusiasts.',
    category: 'Electronics',
    image: 'https://images.unsplash.com/photo-1541140532154-b024d705b90a?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: true,
    quantity: 40,
    rating: 4.7,
    reviews: 234
  },
  {
    name: 'Wireless Earbuds Pro',
    price: 179.99,
    description: 'Premium wireless earbuds with active noise cancellation and crystal-clear sound quality.',
    category: 'Electronics',
    image: 'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: true,
    quantity: 60,
    rating: 4.6,
    reviews: 289
  },
  {
    name: 'Kitchen Knife Set',
    price: 119.99,
    description: 'Professional-grade kitchen knife set with ergonomic handles and razor-sharp stainless steel blades.',
    category: 'Home & Kitchen',
    image: 'https://images.unsplash.com/photo-1593618998160-e34014e67546?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: true,
    quantity: 30,
    rating: 4.8,
    reviews: 145
  },
  {
    name: 'Minimalist Desk Organizer',
    price: 79.99,
    description: 'Clean and modern desk organizer to keep your workspace tidy and organized.',
    category: 'Home & Office',
    image: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 45,
    rating: 4.9,
    reviews: 156
  },
  {
    name: 'Artisan Coffee Mug Set',
    price: 49.99,
    description: 'Handcrafted ceramic coffee mugs perfect for your morning coffee ritual.',
    category: 'Home & Kitchen',
    image: 'https://images.unsplash.com/photo-1514228742587-6b1558fcf93a?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 80,
    rating: 4.7,
    reviews: 94
  },
  {
    name: 'Sustainable Water Bottle',
    price: 34.99,
    description: 'Eco-friendly water bottle made from sustainable materials, perfect for daily hydration.',
    category: 'Lifestyle',
    image: 'https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 100,
    rating: 4.5,
    reviews: 203
  },
  {
    name: 'Wireless Charging Pad',
    price: 59.99,
    description: 'Fast wireless charging pad compatible with all Qi-enabled devices.',
    category: 'Electronics',
    image: 'https://images.unsplash.com/photo-1609592806596-b43bada2f4b8?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 55,
    rating: 4.4,
    reviews: 112
  },
  {
    name: 'Luxury Candle Collection',
    price: 89.99,
    description: 'Premium scented candles made with natural soy wax and essential oils.',
    category: 'Home & Decor',
    image: 'https://images.unsplash.com/photo-1602874801006-e26d3d17d0a5?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 35,
    rating: 4.8,
    reviews: 145
  },
  {
    name: 'Yoga Mat Premium',
    price: 69.99,
    description: 'High-quality yoga mat with superior grip and cushioning for all yoga practices.',
    category: 'Fitness',
    image: 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 70,
    rating: 4.6,
    reviews: 187
  },
  {
    name: 'Bluetooth Speaker Portable',
    price: 79.99,
    description: 'Compact portable speaker with powerful sound and waterproof design.',
    category: 'Electronics',
    image: 'https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 65,
    rating: 4.5,
    reviews: 156
  },
  {
    name: 'Leather Laptop Bag',
    price: 129.99,
    description: 'Premium leather laptop bag with multiple compartments and professional design.',
    category: 'Accessories',
    image: 'https://images.unsplash.com/photo-1553062407-98eeb64c6a62?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 40,
    rating: 4.8,
    reviews: 98
  },
  {
    name: 'Smart Home Security Camera',
    price: 199.99,
    originalPrice: 249.99,
    description: 'Advanced security camera with night vision, motion detection, and mobile app control.',
    category: 'Electronics',
    image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 30,
    rating: 4.4,
    reviews: 167
  }
];

// Additional products to reach 28 total
const additionalProducts = [
  {
    name: 'Ceramic Dinnerware Set',
    price: 159.99,
    description: 'Elegant ceramic dinnerware set perfect for everyday dining and special occasions.',
    category: 'Home & Kitchen',
    image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 25,
    rating: 4.7,
    reviews: 123
  },
  {
    name: 'Bamboo Cutting Board Set',
    price: 45.99,
    description: 'Sustainable bamboo cutting boards in various sizes for all your kitchen needs.',
    category: 'Home & Kitchen',
    image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 90,
    rating: 4.9,
    reviews: 201
  },
  {
    name: 'LED Desk Lamp',
    price: 89.99,
    description: 'Adjustable LED desk lamp with multiple brightness levels and USB charging port.',
    category: 'Home & Office',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 50,
    rating: 4.5,
    reviews: 134
  },
  {
    name: 'Stainless Steel Water Bottle',
    price: 29.99,
    description: 'Durable stainless steel water bottle with double-wall insulation.',
    category: 'Lifestyle',
    image: 'https://images.unsplash.com/photo-1602143407151-7111542de6e8?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 120,
    rating: 4.4,
    reviews: 178
  },
  {
    name: 'Wireless Mouse Ergonomic',
    price: 39.99,
    description: 'Ergonomic wireless mouse designed for comfort during long work sessions.',
    category: 'Electronics',
    image: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 85,
    rating: 4.3,
    reviews: 145
  },
  {
    name: 'Essential Oil Diffuser',
    price: 59.99,
    description: 'Ultrasonic essential oil diffuser with LED lighting and timer settings.',
    category: 'Home & Decor',
    image: 'https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 60,
    rating: 4.6,
    reviews: 167
  },
  {
    name: 'Resistance Bands Set',
    price: 24.99,
    description: 'Complete resistance bands set for home workouts and strength training.',
    category: 'Fitness',
    image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 150,
    rating: 4.5,
    reviews: 234
  },
  {
    name: 'Coffee Grinder Electric',
    price: 79.99,
    originalPrice: 99.99,
    description: 'Electric coffee grinder with multiple grind settings for perfect coffee.',
    category: 'Home & Kitchen',
    image: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 40,
    rating: 4.7,
    reviews: 156
  },
  {
    name: 'Tablet Stand Adjustable',
    price: 34.99,
    description: 'Adjustable tablet stand compatible with all tablet sizes and smartphones.',
    category: 'Accessories',
    image: 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 75,
    rating: 4.4,
    reviews: 189
  },
  {
    name: 'Throw Pillow Set',
    price: 49.99,
    description: 'Decorative throw pillows to add comfort and style to any living space.',
    category: 'Home & Decor',
    image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 95,
    rating: 4.6,
    reviews: 123
  },
  {
    name: 'Portable Phone Charger',
    price: 49.99,
    description: 'High-capacity portable charger with fast charging technology.',
    category: 'Electronics',
    image: 'https://images.unsplash.com/photo-1609592806596-b43bada2f4b8?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 110,
    rating: 4.5,
    reviews: 267
  },
  {
    name: 'Reading Glasses Blue Light',
    price: 39.99,
    description: 'Blue light blocking reading glasses to reduce eye strain from screens.',
    category: 'Accessories',
    image: 'https://images.unsplash.com/photo-1574258495973-f010dfbb5371?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 80,
    rating: 4.3,
    reviews: 198
  },
  {
    name: 'Wall Art Canvas Set',
    price: 89.99,
    description: 'Modern canvas wall art set to enhance any room with contemporary style.',
    category: 'Home & Decor',
    image: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=500&h=500&fit=crop&crop=center',
    inStock: true,
    featured: false,
    quantity: 35,
    rating: 4.7,
    reviews: 134
  }
];



// Seed function using direct MongoDB operations
const seedDatabase = async () => {
  try {
    const db = await connectDB();

    // Clear existing data using direct MongoDB operations
    console.log('Clearing existing data...');

    try {
      console.log('Deleting existing users...');
      const userResult = await db.collection('users').deleteMany({});
      console.log(`Deleted ${userResult.deletedCount} users`);

      console.log('Deleting existing products...');
      const productResult = await db.collection('products').deleteMany({});
      console.log(`Deleted ${productResult.deletedCount} products`);
    } catch (deleteError) {
      console.log('Note: Error clearing existing data (this is normal if collections are empty):', deleteError.message);
    }

    // Create admin user using direct MongoDB operations
    console.log('Creating admin user...');
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(adminUser.password, salt);

    const adminData = {
      ...adminUser,
      password: hashedPassword,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.collection('users').insertOne(adminData);
    console.log('Admin user created successfully!');
    console.log('Admin credentials:');
    console.log('Email:', adminUser.email);
    console.log('Password:', adminUser.password);

    // Create products using direct MongoDB operations
    console.log('Creating products...');
    const allProducts = [...products, ...additionalProducts];

    // Add timestamps to all products
    const productsWithTimestamps = allProducts.map(product => ({
      ...product,
      createdAt: new Date(),
      updatedAt: new Date()
    }));

    // Insert all products at once using insertMany
    const result = await db.collection('products').insertMany(productsWithTimestamps);
    console.log(`${result.insertedCount} products created successfully!`);

    console.log('Database seeding completed!');

    // Close the connection
    await mongoose.connection.close();
    console.log('Database connection closed.');

    process.exit(0);
  } catch (error) {
    console.error('Error seeding database:', error);

    // Close the connection on error too
    try {
      await mongoose.connection.close();
    } catch (closeError) {
      console.error('Error closing connection:', closeError.message);
    }

    process.exit(1);
  }
};

// Run the seed function
seedDatabase();
