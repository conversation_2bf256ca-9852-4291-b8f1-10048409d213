import React from 'react';
import { RotateCcw, CheckCircle, XCircle, Clock } from 'lucide-react';

export function Returns() {
  const returnSteps = [
    {
      step: 1,
      title: 'Initiate Return',
      description: 'Contact us within 30 days of purchase to start your return.'
    },
    {
      step: 2,
      title: 'Get Return Label',
      description: 'We\'ll email you a prepaid return shipping label.'
    },
    {
      step: 3,
      title: 'Pack & Ship',
      description: 'Pack the item securely and attach the return label.'
    },
    {
      step: 4,
      title: 'Receive Refund',
      description: 'Get your refund within 5-7 business days after we receive the item.'
    }
  ];

  const returnConditions = [
    {
      icon: CheckCircle,
      title: 'Returnable Items',
      items: [
        'Unused items in original packaging',
        'Items with all tags attached',
        'Items returned within 30 days',
        'Items in original condition'
      ]
    },
    {
      icon: XCircle,
      title: 'Non-Returnable Items',
      items: [
        'Personalized or customized items',
        'Items damaged by misuse',
        'Items without original packaging',
        'Final sale items'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-green-900 via-green-800 to-green-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24 lg:py-32">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6">Returns & Exchanges</h1>
            <p className="text-lg sm:text-xl md:text-2xl text-gray-200 max-w-4xl mx-auto leading-relaxed">
              Easy returns and exchanges to ensure your complete satisfaction.
            </p>
          </div>
        </div>
      </div>

      {/* Return Policy Overview */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">30-Day Return Policy</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              We want you to be completely satisfied with your purchase. If you're not happy with 
              your order, you can return it within 30 days for a full refund.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {returnSteps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-primary text-white rounded-full mb-4 text-xl font-bold">
                  {step.step}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{step.title}</h3>
                <p className="text-gray-600 text-sm">{step.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Return Conditions */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-12 text-center">Return Conditions</h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {returnConditions.map((condition, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-lg">
                <div className="flex items-center mb-6">
                  <condition.icon className={`h-8 w-8 mr-3 ${
                    condition.title === 'Returnable Items' ? 'text-green-500' : 'text-red-500'
                  }`} />
                  <h3 className="text-xl font-semibold text-gray-900">{condition.title}</h3>
                </div>
                <ul className="space-y-3">
                  {condition.items.map((item, itemIndex) => (
                    <li key={itemIndex} className="flex items-start">
                      <span className={`inline-block w-2 h-2 rounded-full mt-2 mr-3 ${
                        condition.title === 'Returnable Items' ? 'bg-green-500' : 'bg-red-500'
                      }`}></span>
                      <span className="text-gray-600">{item}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Additional Information */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <RotateCcw className="h-6 w-6 mr-2 text-brand-primary" />
                Exchanges
              </h3>
              <p className="text-gray-600">
                We currently don't offer direct exchanges. To exchange an item, please return the 
                original item for a refund and place a new order for the desired item.
              </p>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Clock className="h-6 w-6 mr-2 text-brand-primary" />
                Refund Timeline
              </h3>
              <p className="text-gray-600">
                Refunds are processed within 5-7 business days after we receive your returned item. 
                The refund will be credited to your original payment method.
              </p>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Return Shipping</h3>
              <p className="text-gray-600">
                We provide prepaid return shipping labels for all returns within the United States. 
                For international returns, customers are responsible for return shipping costs.
              </p>
            </div>
          </div>

          <div className="mt-12 bg-gray-50 p-8 rounded-2xl">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Start a Return</h3>
            <p className="text-gray-600 mb-6">
              Ready to return an item? Contact our customer service team to get started.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <a href="/contact" className="bg-brand-primary text-white px-6 py-3 rounded-lg hover:bg-brand-primary-hover transition-colors text-center">
                Contact Support
              </a>
              <a href="mailto:<EMAIL>" className="border border-brand-primary text-brand-primary px-6 py-3 rounded-lg hover:bg-brand-light transition-colors text-center">
                Email Returns Team
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
