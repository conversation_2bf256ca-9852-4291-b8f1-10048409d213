import { createContext, useContext, useReducer, ReactNode, useEffect } from 'react';
import { CartItem, Product, CartContextType } from '../types';

// Define action types as constants to avoid string literals
const ADD_ITEM = 'ADD_ITEM';
const REMOVE_ITEM = 'REMOVE_ITEM';
const UPDATE_QUANTITY = 'UPDATE_QUANTITY';
const CLEAR_CART = 'CLEAR_CART';

// Define action types for the reducer
type CartAction =
  | { type: typeof ADD_ITEM; product: Product; quantity?: number }
  | { type: typeof REMOVE_ITEM; productId: string }
  | { type: typeof UPDATE_QUANTITY; productId: string; quantity: number }
  | { type: typeof CLEAR_CART };

// Define the state structure
interface CartState {
  items: CartItem[];
}

// Create context with undefined initial value
const CartContext = createContext<CartContextType | undefined>(undefined);

/**
 * Cart reducer function to handle state updates based on dispatched actions
 */
function cartReducer(state: CartState, action: CartAction): CartState {
  switch (action.type) {
    case ADD_ITEM: {
      const existingItem = state.items.find(item => item.id === action.product.id);
      const quantity = action.quantity || 1;
      
      if (existingItem) {
        return {
          ...state,
          items: state.items.map(item =>
            item.id === action.product.id
              ? { ...item, quantity: item.quantity + quantity }
              : item
          ),
        };
      }
      
      return {
        ...state,
        items: [...state.items, { id: action.product.id, product: action.product, quantity }],
      };
    }
    
    case REMOVE_ITEM:
      return {
        ...state,
        items: state.items.filter(item => item.id !== action.productId),
      };
    
    case UPDATE_QUANTITY:
      // Remove item if quantity is 0 or negative
      if (action.quantity <= 0) {
        return {
          ...state,
          items: state.items.filter(item => item.id !== action.productId),
        };
      }
      
      // Otherwise update the quantity
      return {
        ...state,
        items: state.items.map(item =>
          item.id === action.productId
            ? { ...item, quantity: action.quantity }
            : item
        ),
      };
    
    case CLEAR_CART:
      return { items: [] };
    
    default:
      return state;
  }
}

/**
 * Load cart data from localStorage
 */
const loadCartFromStorage = (): CartState => {
  if (typeof window === 'undefined') return { items: [] };
  
  try {
    const savedCart = localStorage.getItem('cart');
    if (savedCart) {
      return JSON.parse(savedCart);
    }
  } catch (error) {
    console.error('Error loading cart from localStorage:', error);
  }
  
  return { items: [] };
};

/**
 * CartProvider component to wrap the application and provide cart functionality
 */
export function CartProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(cartReducer, loadCartFromStorage());
  
  // Save cart to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem('cart', JSON.stringify(state));
    } catch (error) {
      console.error('Error saving cart to localStorage:', error);
    }
  }, [state]);

  // Action creators
  const addItem = (product: Product, quantity = 1) => {
    dispatch({ type: ADD_ITEM, product, quantity });
  };

  const removeItem = (productId: string) => {
    dispatch({ type: REMOVE_ITEM, productId });
  };

  const updateQuantity = (productId: string, quantity: number) => {
    dispatch({ type: UPDATE_QUANTITY, productId, quantity });
  };

  const clearCart = () => {
    dispatch({ type: CLEAR_CART });
  };

  // Derived state
  const totalItems = state.items.reduce((sum, item) => sum + item.quantity, 0);
  const totalPrice = state.items.reduce(
    (sum, item) => sum + (item.product.price * item.quantity), 
    0
  );

  // Create context value object
  const value: CartContextType = {
    items: state.items,
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    totalItems,
    totalPrice,
  };

  return <CartContext.Provider value={value}>{children}</CartContext.Provider>;
}

/**
 * Custom hook to use the cart context
 * @throws Error if used outside of CartProvider
 */
export function useCart() {
  const context = useContext(CartContext);
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
}