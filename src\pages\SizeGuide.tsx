import React from 'react';
import { Ruler } from 'lucide-react';

export function SizeGuide() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-green-900 via-green-800 to-green-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24 lg:py-32">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6">Size Guide</h1>
            <p className="text-lg sm:text-xl md:text-2xl text-gray-200 max-w-4xl mx-auto leading-relaxed">
              Find the perfect fit with our comprehensive sizing information.
            </p>
          </div>
        </div>
      </div>

      {/* Size Guide Content */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-primary text-white rounded-full mb-4">
              <Ruler className="h-8 w-8" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-4">How to Measure</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Use a flexible measuring tape and follow these guidelines for the most accurate measurements.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Clothing Measurements</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900">Chest/Bust</h4>
                  <p className="text-gray-600 text-sm">Measure around the fullest part of your chest, keeping the tape horizontal.</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Waist</h4>
                  <p className="text-gray-600 text-sm">Measure around your natural waistline, keeping the tape comfortably loose.</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Hips</h4>
                  <p className="text-gray-600 text-sm">Measure around the fullest part of your hips, about 7-9 inches below your waist.</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Inseam</h4>
                  <p className="text-gray-600 text-sm">Measure from the top of your inner thigh to your ankle bone.</p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-6">Shoe Measurements</h3>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900">Foot Length</h4>
                  <p className="text-gray-600 text-sm">Stand on a piece of paper and mark your heel and longest toe. Measure the distance.</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Foot Width</h4>
                  <p className="text-gray-600 text-sm">Measure across the widest part of your foot while standing.</p>
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Best Time to Measure</h4>
                  <p className="text-gray-600 text-sm">Measure your feet in the evening when they're at their largest.</p>
                </div>
              </div>
            </div>
          </div>

          {/* Size Charts */}
          <div className="space-y-12">
            {/* Clothing Size Chart */}
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Clothing Size Chart</h3>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-4 py-2 text-left">Size</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">Chest (inches)</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">Waist (inches)</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">Hips (inches)</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">XS</td>
                      <td className="border border-gray-300 px-4 py-2">32-34</td>
                      <td className="border border-gray-300 px-4 py-2">24-26</td>
                      <td className="border border-gray-300 px-4 py-2">34-36</td>
                    </tr>
                    <tr className="bg-gray-50">
                      <td className="border border-gray-300 px-4 py-2 font-medium">S</td>
                      <td className="border border-gray-300 px-4 py-2">34-36</td>
                      <td className="border border-gray-300 px-4 py-2">26-28</td>
                      <td className="border border-gray-300 px-4 py-2">36-38</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">M</td>
                      <td className="border border-gray-300 px-4 py-2">36-38</td>
                      <td className="border border-gray-300 px-4 py-2">28-30</td>
                      <td className="border border-gray-300 px-4 py-2">38-40</td>
                    </tr>
                    <tr className="bg-gray-50">
                      <td className="border border-gray-300 px-4 py-2 font-medium">L</td>
                      <td className="border border-gray-300 px-4 py-2">38-40</td>
                      <td className="border border-gray-300 px-4 py-2">30-32</td>
                      <td className="border border-gray-300 px-4 py-2">40-42</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">XL</td>
                      <td className="border border-gray-300 px-4 py-2">40-42</td>
                      <td className="border border-gray-300 px-4 py-2">32-34</td>
                      <td className="border border-gray-300 px-4 py-2">42-44</td>
                    </tr>
                    <tr className="bg-gray-50">
                      <td className="border border-gray-300 px-4 py-2 font-medium">XXL</td>
                      <td className="border border-gray-300 px-4 py-2">42-44</td>
                      <td className="border border-gray-300 px-4 py-2">34-36</td>
                      <td className="border border-gray-300 px-4 py-2">44-46</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            {/* Shoe Size Chart */}
            <div>
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Shoe Size Chart</h3>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse border border-gray-300">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="border border-gray-300 px-4 py-2 text-left">US Size</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">EU Size</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">UK Size</th>
                      <th className="border border-gray-300 px-4 py-2 text-left">Foot Length (inches)</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">6</td>
                      <td className="border border-gray-300 px-4 py-2">39</td>
                      <td className="border border-gray-300 px-4 py-2">5.5</td>
                      <td className="border border-gray-300 px-4 py-2">9.25</td>
                    </tr>
                    <tr className="bg-gray-50">
                      <td className="border border-gray-300 px-4 py-2 font-medium">7</td>
                      <td className="border border-gray-300 px-4 py-2">40</td>
                      <td className="border border-gray-300 px-4 py-2">6.5</td>
                      <td className="border border-gray-300 px-4 py-2">9.625</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">8</td>
                      <td className="border border-gray-300 px-4 py-2">41</td>
                      <td className="border border-gray-300 px-4 py-2">7.5</td>
                      <td className="border border-gray-300 px-4 py-2">10</td>
                    </tr>
                    <tr className="bg-gray-50">
                      <td className="border border-gray-300 px-4 py-2 font-medium">9</td>
                      <td className="border border-gray-300 px-4 py-2">42</td>
                      <td className="border border-gray-300 px-4 py-2">8.5</td>
                      <td className="border border-gray-300 px-4 py-2">10.375</td>
                    </tr>
                    <tr>
                      <td className="border border-gray-300 px-4 py-2 font-medium">10</td>
                      <td className="border border-gray-300 px-4 py-2">43</td>
                      <td className="border border-gray-300 px-4 py-2">9.5</td>
                      <td className="border border-gray-300 px-4 py-2">10.75</td>
                    </tr>
                    <tr className="bg-gray-50">
                      <td className="border border-gray-300 px-4 py-2 font-medium">11</td>
                      <td className="border border-gray-300 px-4 py-2">44</td>
                      <td className="border border-gray-300 px-4 py-2">10.5</td>
                      <td className="border border-gray-300 px-4 py-2">11.125</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div className="mt-12 bg-gray-50 p-8 rounded-2xl">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Need Help with Sizing?</h3>
            <p className="text-gray-600 mb-6">
              Still unsure about sizing? Our customer service team can help you find the perfect fit.
            </p>
            <a href="/contact" className="bg-brand-primary text-white px-6 py-3 rounded-lg hover:bg-brand-primary-hover transition-colors">
              Contact Support
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
