{"name": "stripe-payment-server", "version": "1.0.0", "description": "Server for handling Stripe payment intents", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "stripe": "^14.5.0"}, "devDependencies": {"nodemon": "^3.0.1"}}