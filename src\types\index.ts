export interface Review {
  id: string;
  _id?: string; // MongoDB ObjectId
  product: string;
  user: string;
  rating: number;
  title: string;
  comment: string;
  createdAt: string;
  updatedAt: string;
  userName?: string;
}

export interface Product {
  id: string;
  name: string;
  price: number;
  originalPrice?: number;
  image: string;
  images?: string[];
  description: string;
  category: string;
  rating: number;
  reviews: number;
  reviewsRef?: Review[];
  inStock: boolean;
  featured?: boolean;
  quantity?: number;
}

export interface CartItem {
  id: string;
  product: Product;
  quantity: number;
}

export interface CartContextType {
  items: CartItem[];
  addItem: (product: Product, quantity?: number) => void;
  removeItem: (productId: string) => void;
  updateQuantity: (productId: string, quantity: number) => void;
  clearCart: () => void;
  totalItems: number;
  totalPrice: number;
}