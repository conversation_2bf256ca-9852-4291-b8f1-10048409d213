import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../../context/AuthContext';
import { AdminFooter } from './AdminFooter';
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  LogOut,
  Menu,
  X,
  ChevronDown,
  User,
  Home,
  Settings,
  Bell,
  HelpCircle
} from 'lucide-react';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const { pathname } = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const navItems = [
    { name: 'Dashboard', path: '/admin', icon: <LayoutDashboard className="h-5 w-5" /> },
    { name: 'Products', path: '/admin/products', icon: <Package className="h-5 w-5" /> },
    { name: 'Orders', path: '/admin/orders', icon: <ShoppingCart className="h-5 w-5" /> },
    { name: 'Users', path: '/admin/users', icon: <Users className="h-5 w-5" /> },
  ];

  return (
    <div className="min-h-screen bg-gray-100 flex flex-col">
      {/* Mobile sidebar overlay */}
      <div
        className={`fixed inset-0 z-40 lg:hidden ${sidebarOpen ? 'block' : 'hidden'}`}
        onClick={() => setSidebarOpen(false)}
      >
        <div className="fixed inset-0 bg-gray-800 bg-opacity-75 backdrop-blur-sm transition-opacity" />
      </div>

      {/* Sidebar */}
      <div
        className={`fixed inset-y-0 left-0 z-40 w-72 bg-white shadow-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}
      >
        {/* Sidebar header with logo */}
        <div className="flex items-center justify-between h-20 px-6 bg-white border-b border-gray-100">
          <Link to="/admin" className="text-xl font-bold text-gray-800 flex items-center group">
            <div className="bg-green-100 p-1.5 rounded-xl shadow-md mr-3 group-hover:scale-105 transition-transform duration-200 border border-green-200">
              <img src="/logo.jpg" alt="jaisalgoonline" className="h-8 w-auto" />
            </div>
            <div className="flex flex-col">
              <span className="text-lg font-bold text-gray-900">jaisalgoonline</span>
              <span className="text-xs text-green-600 font-semibold bg-green-100 px-2 py-1 rounded-full">Admin Portal</span>
            </div>
          </Link>
          <button
            className="lg:hidden text-gray-600 hover:text-green-600 p-2 rounded-lg hover:bg-green-50"
            onClick={() => setSidebarOpen(false)}
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* User profile section */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center border-2 border-green-500 shadow-md">
              <User className="h-5 w-5 text-green-700" />
            </div>
            <div className="flex flex-col">
              <span className="text-sm font-medium text-gray-900">{user?.firstName} {user?.lastName}</span>
              <span className="text-xs text-green-700">{user?.role === 'admin' ? 'Administrator' : 'Staff'}</span>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="py-4 px-4">
          <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 mb-2">Main</div>
          <nav className="space-y-1">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`group flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${pathname === item.path || (item.path !== '/admin' && pathname.startsWith(item.path)) ? 'bg-green-600 text-white shadow-md' : 'text-gray-700 hover:bg-green-50 hover:text-green-700'}`}
              >
                <div
                  className={`mr-3 ${pathname === item.path || (item.path !== '/admin' && pathname.startsWith(item.path)) ? 'text-white' : 'text-gray-500 group-hover:text-green-700'}`}
                >
                  {item.icon}
                </div>
                {item.name}
              </Link>
            ))}
          </nav>
          
          <div className="text-xs font-semibold text-gray-500 uppercase tracking-wider px-2 mt-6 mb-2">Other</div>
          <nav className="space-y-1">
            <Link 
              to="/"
              className="group flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 text-gray-700 hover:bg-green-50 hover:text-green-700"
            >
              <div className="mr-3 text-gray-500 group-hover:text-green-700">
                <Home className="h-5 w-5" />
              </div>
              Back to Store
            </Link>
            
            <Link 
              to="#"
              className="group flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 text-gray-700 hover:bg-green-50 hover:text-green-700"
            >
              <div className="mr-3 text-gray-500 group-hover:text-green-700">
                <Settings className="h-5 w-5" />
              </div>
              Settings
            </Link>
            
            <Link 
              to="#"
              className="group flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 text-gray-700 hover:bg-green-50 hover:text-green-700"
            >
              <div className="mr-3 text-gray-500 group-hover:text-green-700">
                <HelpCircle className="h-5 w-5" />
              </div>
              Help & Support
            </Link>
          </nav>
        </div>

        {/* Logout button */}
        <div className="absolute bottom-0 w-full border-t border-gray-200 p-4 bg-gray-50">
          <button
            onClick={handleLogout}
            className="flex items-center w-full px-4 py-3 text-sm font-medium text-red-600 rounded-lg hover:bg-red-50 transition-all duration-200"
          >
            <LogOut className="mr-3 h-5 w-5" />
            Logout
          </button>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-72 flex flex-col min-h-screen w-full overflow-x-hidden">
        {/* Top navigation */}
        <header className="sticky top-0 z-50 bg-gradient-to-r from-white to-green-50/30 border-b border-green-200 shadow-lg backdrop-blur-sm">
          <div className="flex justify-between items-center h-16 px-4 sm:px-6 lg:px-8">
            <div className="flex items-center space-x-4">
              <button
                className="lg:hidden text-gray-700 hover:text-green-600 transition-colors p-2 rounded-lg hover:bg-green-100"
                onClick={() => setSidebarOpen(true)}
              >
                <Menu className="h-6 w-6" />
              </button>

              <div className="flex items-center">
                <h1 className="text-xl font-bold text-gray-800 hidden md:block">
                  {pathname === '/admin' && '📊 Dashboard'}
                  {pathname.includes('/admin/products') && '📦 Products Management'}
                  {pathname.includes('/admin/orders') && '🛒 Orders Management'}
                  {pathname.includes('/admin/users') && '👥 Users Management'}
                </h1>
              </div>
            </div>

            {/* User dropdown */}
            <div className="flex items-center space-x-3">
              {/* Notification bell */}
              <button className="p-2 text-gray-500 hover:text-green-600 rounded-xl hover:bg-green-100 transition-all duration-200 relative">
                <Bell className="h-5 w-5" />
                <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full"></span>
              </button>

              {/* Help button */}
              <button className="p-2 text-gray-500 hover:text-green-600 rounded-xl hover:bg-green-100 transition-all duration-200">
                <HelpCircle className="h-5 w-5" />
              </button>

              <div className="relative">
                <div>
                  <button
                    className="flex items-center text-sm rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 hover:bg-green-50 p-2"
                    onClick={() => setUserMenuOpen(!userMenuOpen)}
                  >
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-10 rounded-xl bg-gradient-to-br from-green-600 to-emerald-600 flex items-center justify-center text-white shadow-lg border-2 border-white">
                        <span className="text-sm font-bold">
                          {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}
                        </span>
                      </div>
                      <div className="hidden md:block text-left">
                        <p className="text-sm font-semibold text-gray-800">{user?.firstName} {user?.lastName}</p>
                        <p className="text-xs text-green-600 font-medium">{user?.role === 'admin' ? 'Administrator' : 'Staff'}</p>
                      </div>
                      <ChevronDown className="h-4 w-4 text-gray-500 hidden md:block" />
                    </div>
                  </button>
                </div>

                {userMenuOpen && (
                  <div className="origin-top-right absolute right-0 mt-3 w-64 rounded-2xl shadow-2xl py-2 bg-white ring-1 ring-black ring-opacity-5 z-50 border border-green-100">
                    <div className="px-4 py-4 border-b border-green-100 bg-gradient-to-r from-green-50 to-emerald-50 rounded-t-2xl">
                      <p className="text-sm text-gray-800 font-semibold">{user?.email}</p>
                      <p className="text-xs text-green-700 mt-1 font-medium bg-green-100 px-2 py-1 rounded-full inline-block">
                        {user?.role === 'admin' ? '👑 Administrator' : '👤 User'}
                      </p>
                    </div>
                    <div className="py-2">
                      <Link
                        to="/profile"
                        className="block px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 flex items-center transition-colors duration-200"
                        onClick={() => setUserMenuOpen(false)}
                      >
                        <User className="h-4 w-4 mr-3 text-gray-500" />
                        My Profile
                      </Link>
                      <Link
                        to="/"
                        className="block px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 flex items-center transition-colors duration-200"
                        onClick={() => setUserMenuOpen(false)}
                      >
                        <Home className="h-4 w-4 mr-3 text-gray-500" />
                        View Store
                      </Link>
                    </div>
                    <div className="border-t border-gray-100 pt-2">
                      <button
                        onClick={handleLogout}
                        className="block w-full text-left px-4 py-3 text-sm text-red-600 hover:bg-red-50 flex items-center transition-colors duration-200"
                      >
                        <LogOut className="h-4 w-4 mr-3 text-red-500" />
                        Sign out
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 py-8 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto w-full">
          <div className="bg-gradient-to-br from-white to-green-50/20 rounded-2xl shadow-xl border border-green-100 p-8 mb-8 min-h-[calc(100vh-12rem)]">
            {children}
          </div>
        </main>
        
        {/* Footer */}
        <div className="w-full lg:pl-0">
          <AdminFooter />
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;