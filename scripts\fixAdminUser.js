const mongoose = require('mongoose');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// Connect to MongoDB and fix admin user
const fixAdminUser = async () => {
  try {
    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI;
    console.log('Attempting to connect to MongoDB...');
    
    if (!mongoUri) {
      throw new Error('MONGODB_URI environment variable is not set. Please check your .env file.');
    }
    
    await mongoose.connect(mongo<PERSON><PERSON>, {
      serverSelectionTimeoutMS: 60000,
      socketTimeoutMS: 60000,
      connectTimeoutMS: 60000,
      maxPoolSize: 5,
    });
    
    console.log('MongoDB Connected...');
    
    // Get direct database access
    const db = mongoose.connection.db;
    
    // Find and update the admin user
    console.log('Looking for admin user...');
    const adminUser = await db.collection('users').findOne({ email: '<EMAIL>' });
    
    if (adminUser) {
      console.log('Found admin user:', {
        email: adminUser.email,
        role: adminUser.role,
        isAdmin: adminUser.isAdmin
      });
      
      // Update the admin user to ensure role is set correctly
      const updateResult = await db.collection('users').updateOne(
        { email: '<EMAIL>' },
        { 
          $set: { role: 'admin' },
          $unset: { isAdmin: "" } // Remove the isAdmin field if it exists
        }
      );
      
      console.log('Update result:', updateResult);
      
      // Verify the update
      const updatedUser = await db.collection('users').findOne({ email: '<EMAIL>' });
      console.log('Updated admin user:', {
        email: updatedUser.email,
        role: updatedUser.role,
        isAdmin: updatedUser.isAdmin
      });
      
      console.log('Admin user role fixed successfully!');
    } else {
      console.log('Admin user not found. Please run the seed script first.');
    }
    
    // Close the connection
    await mongoose.connection.close();
    console.log('Database connection closed.');
    
    process.exit(0);
  } catch (error) {
    console.error('Error fixing admin user:', error);
    
    try {
      await mongoose.connection.close();
    } catch (closeError) {
      console.error('Error closing connection:', closeError.message);
    }
    
    process.exit(1);
  }
};

// Run the fix function
fixAdminUser();
