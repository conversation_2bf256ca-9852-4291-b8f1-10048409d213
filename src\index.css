@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for better design hierarchy */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }
}

@layer components {
  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Custom button styles */
  .btn-primary {
    @apply bg-green-700 text-white px-6 py-3 rounded-full font-semibold hover:bg-green-800 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply border-2 border-green-700 text-green-700 px-6 py-3 rounded-full font-semibold hover:bg-green-700 hover:text-white transition-all duration-200;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1;
  }

  /* Input styles */
  .input-field {
    @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-colors;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@layer components {
  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Enhanced button styles */
  .btn-primary {
    @apply bg-gradient-to-r from-green-600 to-green-700 text-white px-8 py-4 rounded-2xl font-semibold hover:from-green-700 hover:to-green-800 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl backdrop-blur-sm;
  }

  .btn-secondary {
    @apply border-2 border-green-600 text-green-700 px-8 py-4 rounded-2xl font-semibold hover:bg-green-600 hover:text-white transition-all duration-300 backdrop-blur-sm shadow-lg hover:shadow-xl;
  }

  .btn-ghost {
    @apply text-green-700 px-6 py-3 rounded-xl font-medium hover:bg-green-50 transition-all duration-200;
  }

  /* Enhanced card styles */
  .card {
    @apply bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1;
  }

  .card-glass {
    @apply bg-white/90 rounded-2xl shadow-xl border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1;
  }

  .card-elevated {
    @apply bg-white rounded-2xl shadow-xl border-0 overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2;
  }

  /* Enhanced input styles */
  .input-field {
    @apply w-full px-6 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300 bg-gray-50/50 hover:bg-white focus:bg-white shadow-sm focus:shadow-lg;
  }

  .input-glass {
    @apply w-full px-6 py-4 border border-white/20 rounded-2xl focus:outline-none focus:ring-2 focus:ring-green-400 focus:border-transparent transition-all duration-300 bg-white/10 backdrop-blur-md placeholder-white/70 text-white;
  }

  /* Enhanced typography */
  .heading-1 {
    @apply text-5xl md:text-7xl lg:text-8xl font-bold leading-tight tracking-tight;
    font-family: 'Playfair Display', serif;
  }

  .heading-2 {
    @apply text-4xl md:text-5xl lg:text-6xl font-bold leading-tight tracking-tight;
    font-family: 'Playfair Display', serif;
  }

  .heading-3 {
    @apply text-3xl md:text-4xl font-bold leading-tight tracking-tight;
    font-family: 'Playfair Display', serif;
  }

  .heading-4 {
    @apply text-2xl md:text-3xl font-semibold leading-tight;
    font-family: 'Playfair Display', serif;
  }

  .body-large {
    @apply text-lg md:text-xl leading-relaxed font-light;
  }

  .body-text {
    @apply text-base leading-relaxed;
  }

  .text-accent {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-emerald-600 font-semibold;
  }

  /* Enhanced animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.8s ease-out;
  }

  .animate-slide-up {
    animation: slideUp 0.8s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.8s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.8s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.6s ease-out;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 3s ease-in-out infinite;
  }
}

@layer utilities {
  /* Custom brand colors */
  .bg-brand-primary {
    background-color: #006531;
  }

  .text-brand-primary {
    color: #006531;
  }

  .border-brand-primary {
    border-color: #006531;
  }

  .bg-brand-primary-hover:hover {
    background-color: #004d26;
  }

  .bg-brand-light {
    background-color: #f0f9f4;
  }

  /* Enhanced animations */
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  /* Enhanced gradient utilities */
  .gradient-text {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-green-500 to-emerald-600;
  }

  .gradient-text-gold {
    @apply text-transparent bg-clip-text bg-gradient-to-r from-yellow-500 to-orange-500;
  }

  .gradient-bg-primary {
    @apply bg-gradient-to-br from-green-600 via-green-700 to-emerald-800;
  }

  .gradient-bg-hero {
    @apply bg-gradient-to-br from-green-900 via-green-800 to-emerald-900;
  }

  .gradient-bg-card {
    @apply bg-gradient-to-br from-white to-green-50/30;
  }

  /* Glass morphism effects */
  .glass {
    @apply bg-white/20 border border-white/30;
  }

  .glass-dark {
    @apply bg-black/20 border border-white/20;
  }

  /* Shadow utilities */
  .shadow-soft {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }

  .shadow-medium {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }

  .shadow-strong {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }

  .shadow-green {
    box-shadow: 0 10px 30px rgba(34, 197, 94, 0.2);
  }

  /* Decorative elements */
  .pattern-dots {
    background-image: radial-gradient(circle, rgba(34, 197, 94, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .pattern-grid {
    background-image: linear-gradient(rgba(34, 197, 94, 0.1) 1px, transparent 1px),
                      linear-gradient(90deg, rgba(34, 197, 94, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* Custom scrollbar */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }
}
