const express = require('express');
const router = express.Router();
const Stats = require('../models/Stats');
const User = require('../models/User');
const Order = require('../models/Order');
const Product = require('../models/Product');
const { protect, admin } = require('../middleware/auth');

/**
 * @route   GET /api/stats
 * @desc    Get store statistics
 * @access  Private/Admin
 */
router.get('/', protect, admin, async (req, res) => {
  try {
    // Get or create stats document
    let stats = await Stats.findOne({});
    if (!stats) {
      stats = new Stats();
      await stats.save();
    }

    // Get additional real-time stats
    const totalUsers = await User.countDocuments();
    const totalOrders = await Order.countDocuments();
    const totalProducts = await Product.countDocuments();
    const totalRevenue = await Order.aggregate([
      { $match: { 'paymentInfo.paymentStatus': 'succeeded' } },
      { $group: { _id: null, total: { $sum: '$total' } } }
    ]);

    // Get recent orders
    const recentOrders = await Order.find({})
      .sort({ createdAt: -1 })
      .limit(10)
      .populate('user', 'firstName lastName email');

    // Get low stock products
    const lowStockProducts = await Product.find({ inStock: false })
      .sort({ createdAt: -1 })
      .limit(10);

    // Combine all stats
    const combinedStats = {
      totalUsers,
      totalOrders,
      totalProducts,
      totalRevenue: totalRevenue.length > 0 ? totalRevenue[0].total : 0,
      dailyRevenue: stats.dailyRevenue,
      monthlySales: stats.monthlySales,
      topSellingProducts: stats.topSellingProducts,
      topCategories: stats.topCategories,
      recentOrders,
      lowStockProducts
    };

    res.json(combinedStats);
  } catch (error) {
    console.error('Error fetching stats:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/stats/revenue
 * @desc    Get revenue statistics
 * @access  Private/Admin
 */
router.get('/revenue', protect, admin, async (req, res) => {
  try {
    const { period = 'daily', limit = 30 } = req.query;
    let stats = await Stats.findOne({});
    
    if (!stats) {
      stats = new Stats();
      await stats.save();
    }

    let revenueData;
    
    if (period === 'daily') {
      // Get daily revenue for the last X days
      revenueData = stats.dailyRevenue
        .sort((a, b) => new Date(b.date) - new Date(a.date))
        .slice(0, parseInt(limit));
    } else if (period === 'monthly') {
      // Get monthly revenue
      revenueData = stats.monthlySales
        .sort((a, b) => {
          if (a.year !== b.year) return b.year - a.year;
          return b.month - a.month;
        })
        .slice(0, parseInt(limit));
    }

    res.json(revenueData);
  } catch (error) {
    console.error('Error fetching revenue stats:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/stats/products
 * @desc    Get product statistics
 * @access  Private/Admin
 */
router.get('/products', protect, admin, async (req, res) => {
  try {
    let stats = await Stats.findOne({});
    
    if (!stats) {
      stats = new Stats();
      await stats.save();
    }

    // Populate product details for top selling products
    const topSellingProducts = await Promise.all(
      stats.topSellingProducts.map(async (item) => {
        const product = await Product.findById(item.product);
        return {
          ...item.toObject(),
          productDetails: product ? {
            name: product.name,
            image: product.image,
            price: product.price
          } : null
        };
      })
    );

    res.json({
      topSellingProducts,
      topCategories: stats.topCategories
    });
  } catch (error) {
    console.error('Error fetching product stats:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   POST /api/stats/refresh
 * @desc    Refresh statistics (recalculate from orders)
 * @access  Private/Admin
 */
router.post('/refresh', protect, admin, async (req, res) => {
  try {
    // Create new stats object
    const stats = new Stats();
    
    // Count users
    stats.totalUsers = await User.countDocuments();
    
    // Get all orders
    const orders = await Order.find({ 'paymentInfo.paymentStatus': 'succeeded' });
    
    stats.totalOrders = orders.length;
    stats.totalRevenue = orders.reduce((sum, order) => sum + order.total, 0);
    
    // Process daily revenue
    const dailyRevenueMap = new Map();
    
    orders.forEach(order => {
      const orderDate = new Date(order.createdAt);
      orderDate.setHours(0, 0, 0, 0);
      const dateString = orderDate.toISOString();
      
      if (dailyRevenueMap.has(dateString)) {
        dailyRevenueMap.set(dateString, dailyRevenueMap.get(dateString) + order.total);
      } else {
        dailyRevenueMap.set(dateString, order.total);
      }
    });
    
    stats.dailyRevenue = Array.from(dailyRevenueMap).map(([date, amount]) => ({
      date: new Date(date),
      amount
    }));
    
    // Process monthly sales
    const monthlySalesMap = new Map();
    
    orders.forEach(order => {
      const orderDate = new Date(order.createdAt);
      const month = orderDate.getMonth();
      const year = orderDate.getFullYear();
      const key = `${year}-${month}`;
      
      if (monthlySalesMap.has(key)) {
        const data = monthlySalesMap.get(key);
        data.amount += order.total;
        data.orders += 1;
      } else {
        monthlySalesMap.set(key, {
          month,
          year,
          amount: order.total,
          orders: 1
        });
      }
    });
    
    stats.monthlySales = Array.from(monthlySalesMap.values());
    
    // Process top selling products
    const productSalesMap = new Map();
    
    for (const order of orders) {
      for (const item of order.items) {
        const productId = item.product.toString();
        
        if (productSalesMap.has(productId)) {
          const data = productSalesMap.get(productId);
          data.quantity += item.quantity;
          data.revenue += item.price * item.quantity;
        } else {
          productSalesMap.set(productId, {
            product: item.product,
            quantity: item.quantity,
            revenue: item.price * item.quantity
          });
        }
      }
    }
    
    stats.topSellingProducts = Array.from(productSalesMap.values())
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);
    
    // Process top categories
    const categorySalesMap = new Map();
    
    for (const order of orders) {
      for (const item of order.items) {
        // Get product to find its category
        const product = await Product.findById(item.product);
        if (product) {
          const category = product.category;
          
          if (categorySalesMap.has(category)) {
            categorySalesMap.set(
              category, 
              categorySalesMap.get(category) + (item.price * item.quantity)
            );
          } else {
            categorySalesMap.set(category, item.price * item.quantity);
          }
        }
      }
    }
    
    stats.topCategories = Array.from(categorySalesMap).map(([category, sales]) => ({
      category,
      sales
    })).sort((a, b) => b.sales - a.sales).slice(0, 10);
    
    // Delete old stats and save new one
    await Stats.deleteMany({});
    await stats.save();
    
    res.json({ message: 'Statistics refreshed successfully', stats });
  } catch (error) {
    console.error('Error refreshing stats:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;