import React, { useState } from 'react';
import { Star } from 'lucide-react';

interface ReviewFormProps {
  onSubmit: (review: {
    rating: number;
    title: string;
    comment: string;
  }) => void;
  onCancel: () => void;
}

export default function ReviewForm({ onSubmit, onCancel }: ReviewFormProps) {
  const [rating, setRating] = useState<number>(5);
  const [title, setTitle] = useState<string>('');
  const [comment, setComment] = useState<string>('');
  const [errors, setErrors] = useState<{
    rating?: string;
    title?: string;
    comment?: string;
  }>({});

  const validateForm = () => {
    const newErrors: {
      rating?: string;
      title?: string;
      comment?: string;
    } = {};

    if (rating < 1 || rating > 5) {
      newErrors.rating = 'Rating must be between 1 and 5';
    }

    if (!title.trim()) {
      newErrors.title = 'Title is required';
    } else if (title.length > 100) {
      newErrors.title = 'Title cannot be more than 100 characters';
    }

    if (!comment.trim()) {
      newErrors.comment = 'Comment is required';
    } else if (comment.length > 1000) {
      newErrors.comment = 'Comment cannot be more than 1000 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      onSubmit({
        rating,
        title,
        comment,
      });
    }
  };

  return (
    <div className="bg-gradient-to-br from-white to-green-50/30 p-8 rounded-2xl shadow-lg border border-green-100 review-form">
      <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
        <span className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mr-3">
          <span className="text-white text-sm">✍️</span>
        </span>
        Write a Review
      </h2>
      <form onSubmit={handleSubmit}>
        <div className="mb-6">
          <label className="block text-sm font-semibold text-gray-800 mb-3">Rating</label>
          <div className="flex items-center space-x-2 p-4 bg-white/70 rounded-xl border border-green-200">
            {[1, 2, 3, 4, 5].map((value) => (
              <button
                key={value}
                type="button"
                onClick={() => setRating(value)}
                className="focus:outline-none hover:scale-110 transition-transform duration-200"
              >
                <Star
                  className={`h-8 w-8 ${value <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300 hover:text-yellow-200'}`}
                />
              </button>
            ))}
            <span className="ml-3 text-sm font-medium text-gray-700 bg-green-100 px-3 py-1 rounded-full">
              {rating} out of 5
            </span>
          </div>
          {errors.rating && <p className="text-red-500 text-sm mt-2 font-medium">{errors.rating}</p>}
        </div>

        <div className="mb-6">
          <label htmlFor="title" className="block text-sm font-semibold text-gray-800 mb-3">
            Review Title
          </label>
          <input
            type="text"
            id="title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            className="w-full px-4 py-3 border-2 border-green-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white/70 hover:bg-white"
            placeholder="Summarize your experience"
            maxLength={100}
          />
          {errors.title && <p className="text-red-500 text-sm mt-2 font-medium">{errors.title}</p>}
        </div>

        <div className="mb-6">
          <label htmlFor="comment" className="block text-sm font-semibold text-gray-800 mb-3">
            Your Review
          </label>
          <textarea
            id="comment"
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            rows={4}
            className="w-full px-4 py-3 border-2 border-green-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 bg-white/70 hover:bg-white resize-none"
            placeholder="Share your detailed experience with this product"
            maxLength={1000}
          ></textarea>
          <div className="flex justify-between items-center mt-2">
            <span className="text-xs text-gray-500">{comment.length}/1000 characters</span>
            {errors.comment && <p className="text-red-500 text-sm font-medium">{errors.comment}</p>}
          </div>
        </div>

        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={onCancel}
            className="px-6 py-3 border-2 border-gray-300 rounded-xl text-sm font-semibold text-gray-700 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200"
          >
            Cancel
          </button>
          <button
            type="submit"
            className="px-6 py-3 border border-transparent rounded-xl shadow-lg text-sm font-semibold text-white bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transform hover:scale-105 transition-all duration-200"
          >
            Submit Review
          </button>
        </div>
      </form>
    </div>
  );
}