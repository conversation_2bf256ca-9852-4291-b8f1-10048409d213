import React, { useState, useEffect } from 'react';
import { useAuth } from '../../context/AuthContext';
import AdminLayout from './components/AdminLayout';
import { Plus, Edit, Trash2, AlertCircle, Settings } from 'lucide-react';
import { API_URL } from '../../utils/env';

interface Category {
  _id: string;
  name: string;
  description?: string;
  productCount: number;
  createdAt: string;
}

interface CategoriesState {
  categories: Category[];
  loading: boolean;
  error: string | null;
}

interface CategoryFormData {
  _id?: string;
  name: string;
  description: string;
}

const Categories: React.FC = () => {
  const { token } = useAuth();
  const [state, setState] = useState<CategoriesState>({
    categories: [],
    loading: true,
    error: null,
  });

  const [showForm, setShowForm] = useState(false);
  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    description: '',
  });
  const [isEditing, setIsEditing] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [deleteConfirm, setDeleteConfirm] = useState<string | null>(null);

  useEffect(() => {
    fetchCategories();
  }, [token]);

  const fetchCategories = async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      // Fetch all products to calculate category counts
      const productsResponse = await fetch(`${API_URL}/api/products?limit=1000`, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (!productsResponse.ok) {
        throw new Error('Failed to fetch products');
      }

      const productsData = await productsResponse.json();
      const products = productsData.products || [];

      // Calculate category counts
      const categoryMap = new Map<string, number>();
      products.forEach((product: any) => {
        if (product.category) {
          categoryMap.set(product.category, (categoryMap.get(product.category) || 0) + 1);
        }
      });

      // Create category objects
      const categories: Category[] = Array.from(categoryMap.entries()).map(([name, count]) => ({
        _id: name, // Using name as ID for simplicity
        name,
        description: '',
        productCount: count,
        createdAt: new Date().toISOString(),
      }));

      setState(prev => ({
        ...prev,
        categories: categories.sort((a, b) => a.name.localeCompare(b.name)),
        loading: false,
      }));
    } catch (err) {
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : 'An error occurred',
        loading: false,
      }));
    }
  };

  const validateForm = () => {
    const errors: Record<string, string> = {};
    if (!formData.name.trim()) errors.name = 'Category name is required';
    if (formData.name.length > 50) errors.name = 'Category name must be less than 50 characters';
    
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    try {
      // For now, we'll just add to the local state since we don't have a categories API
      if (isEditing) {
        setState(prev => ({
          ...prev,
          categories: prev.categories.map(cat => 
            cat._id === formData._id 
              ? { ...cat, name: formData.name, description: formData.description }
              : cat
          )
        }));
      } else {
        const newCategory: Category = {
          _id: formData.name,
          name: formData.name,
          description: formData.description,
          productCount: 0,
          createdAt: new Date().toISOString(),
        };
        setState(prev => ({
          ...prev,
          categories: [...prev.categories, newCategory].sort((a, b) => a.name.localeCompare(b.name))
        }));
      }

      setShowForm(false);
      resetForm();
    } catch (err) {
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : 'An error occurred'
      }));
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
    });
    setFormErrors({});
    setIsEditing(false);
  };

  const handleAddCategory = () => {
    resetForm();
    setShowForm(true);
  };

  const handleEditCategory = (category: Category) => {
    setFormData({
      _id: category._id,
      name: category.name,
      description: category.description || '',
    });
    setIsEditing(true);
    setShowForm(true);
  };

  const handleDeleteCategory = async (categoryId: string) => {
    try {
      const category = state.categories.find(c => c._id === categoryId);
      if (category && category.productCount > 0) {
        setState(prev => ({
          ...prev,
          error: `Cannot delete category "${category.name}" because it has ${category.productCount} products. Please move or delete the products first.`
        }));
        setDeleteConfirm(null);
        return;
      }

      setState(prev => ({
        ...prev,
        categories: prev.categories.filter(cat => cat._id !== categoryId)
      }));
      setDeleteConfirm(null);
    } catch (err) {
      setState(prev => ({
        ...prev,
        error: err instanceof Error ? err.message : 'An error occurred'
      }));
    }
  };

  return (
    <AdminLayout>
      <div className="mb-6 flex justify-between items-center">
        <div className="flex items-center">
          <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mr-3">
            <Settings className="h-5 w-5 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900">Categories</h1>
        </div>
        <button
          onClick={handleAddCategory}
          className="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 transition-all duration-200 flex items-center shadow-lg hover:shadow-xl transform hover:scale-105"
        >
          <Plus className="h-5 w-5 mr-2" />
          Add Category
        </button>
      </div>

      {state.error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 mb-6">
          <div className="flex items-center">
            <AlertCircle className="h-6 w-6 text-red-500 mr-3" />
            <p className="text-red-700">{state.error}</p>
          </div>
        </div>
      )}

      {/* Categories Table */}
      <div className="bg-gradient-to-br from-white to-green-50/30 rounded-2xl shadow-xl overflow-hidden border border-green-100">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-green-200">
            <thead className="bg-gradient-to-r from-green-600 to-emerald-600">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider">
                  Category Name
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-4 text-left text-xs font-bold text-white uppercase tracking-wider">
                  Products
                </th>
                <th className="px-6 py-4 text-right text-xs font-bold text-white uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-green-100">
              {state.loading ? (
                <tr>
                  <td colSpan={4} className="px-6 py-4 text-center">
                    <div className="flex justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
                    </div>
                  </td>
                </tr>
              ) : state.categories.length === 0 ? (
                <tr>
                  <td colSpan={4} className="px-6 py-4 text-center text-gray-500">
                    No categories found
                  </td>
                </tr>
              ) : (
                state.categories.map((category) => (
                  <tr key={category._id} className="hover:bg-green-50/50 transition-colors duration-200">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-semibold text-gray-900">{category.name}</div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-600">
                        {category.description || 'No description'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {category.productCount} products
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => handleEditCategory(category)}
                          className="p-2 text-blue-600 hover:text-white hover:bg-blue-600 rounded-lg transition-all duration-200 hover:scale-110"
                          title="Edit Category"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => setDeleteConfirm(category._id)}
                          className="p-2 text-red-600 hover:text-white hover:bg-red-600 rounded-lg transition-all duration-200 hover:scale-110"
                          title="Delete Category"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>
    </AdminLayout>
  );
};

export default Categories;
