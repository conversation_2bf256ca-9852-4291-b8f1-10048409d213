import React from 'react';
import { Truck, Clock, MapPin, Package } from 'lucide-react';

export function ShippingInfo() {
  const shippingOptions = [
    {
      icon: Truck,
      name: 'Standard Shipping',
      time: '5-7 Business Days',
      cost: 'Free on orders over $50',
      description: 'Our most popular shipping option with reliable delivery times.'
    },
    {
      icon: Clock,
      name: 'Express Shipping',
      time: '2-3 Business Days',
      cost: '$9.99',
      description: 'Faster delivery for when you need your items quickly.'
    },
    {
      icon: Package,
      name: 'Overnight Shipping',
      time: '1 Business Day',
      cost: '$19.99',
      description: 'Next-day delivery for urgent orders placed before 2 PM.'
    },
    {
      icon: MapPin,
      name: 'International Shipping',
      time: '7-14 Business Days',
      cost: 'Calculated at checkout',
      description: 'We ship to most countries worldwide.'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div className="bg-gradient-to-br from-green-900 via-green-800 to-green-900 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24 lg:py-32">
          <div className="text-center">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-6">Shipping Information</h1>
            <p className="text-lg sm:text-xl md:text-2xl text-gray-200 max-w-4xl mx-auto leading-relaxed">
              Fast, reliable shipping options to get your orders to you quickly and safely.
            </p>
          </div>
        </div>
      </div>

      {/* Shipping Options */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Shipping Options</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Choose the shipping method that works best for you.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {shippingOptions.map((option, index) => (
              <div key={index} className="bg-gray-50 rounded-2xl p-6 text-center hover:shadow-lg transition-shadow">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-brand-primary text-white rounded-full mb-4">
                  <option.icon className="h-8 w-8" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">{option.name}</h3>
                <p className="text-brand-primary font-bold mb-2">{option.time}</p>
                <p className="text-gray-900 font-medium mb-3">{option.cost}</p>
                <p className="text-gray-600 text-sm">{option.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Shipping Details */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">Shipping Details</h2>
          
          <div className="space-y-8">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Processing Time</h3>
              <p className="text-gray-600">
                Orders are typically processed within 1-2 business days. Orders placed on weekends 
                or holidays will be processed the next business day.
              </p>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Shipping Restrictions</h3>
              <ul className="list-disc pl-6 text-gray-600 space-y-2">
                <li>We currently ship to all 50 US states and most international locations</li>
                <li>Some items may have shipping restrictions due to size or regulations</li>
                <li>PO Boxes are accepted for standard shipping only</li>
                <li>Expedited shipping is not available to PO Boxes</li>
              </ul>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Order Tracking</h3>
              <p className="text-gray-600">
                Once your order ships, you'll receive a tracking number via email. You can track 
                your package on our website or directly with the shipping carrier.
              </p>
            </div>

            <div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Delivery Issues</h3>
              <p className="text-gray-600">
                If your package is lost or damaged during shipping, please contact us immediately. 
                We'll work with the carrier to resolve the issue and ensure you receive your order.
              </p>
            </div>
          </div>

          <div className="mt-12 bg-white p-8 rounded-2xl shadow-lg">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">Need Help?</h3>
            <p className="text-gray-600 mb-4">
              Have questions about shipping? Our customer service team is here to help.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <a href="/contact" className="bg-brand-primary text-white px-6 py-3 rounded-lg hover:bg-brand-primary-hover transition-colors text-center">
                Contact Support
              </a>
              <a href="/faq" className="border border-brand-primary text-brand-primary px-6 py-3 rounded-lg hover:bg-brand-light transition-colors text-center">
                View FAQ
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
