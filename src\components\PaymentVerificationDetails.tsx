import { useState, useEffect } from 'react';
import { ExternalLink } from 'lucide-react';
import { getPaymentRecords, PaymentRecord, PaymentVerificationStatus } from '../utils/stripePaymentVerification';

interface PaymentVerificationDetailsProps {
  showTitle?: boolean;
}

export function PaymentVerificationDetails({ showTitle = true }: PaymentVerificationDetailsProps) {
  const [paymentRecords, setPaymentRecords] = useState<PaymentRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Load payment records from local storage
    const records = getPaymentRecords();
    setPaymentRecords(records);
    setIsLoading(false);
  }, []);

  // Format date for display
  const formatDate = (date: Date) => {
    if (!(date instanceof Date)) {
      // If it's a string representation of a date, convert it
      date = new Date(date);
    }
    return date.toLocaleString();
  };

  // Get status badge color
  const getStatusColor = (status: PaymentVerificationStatus) => {
    switch (status) {
      case PaymentVerificationStatus.SUCCEEDED:
        return 'bg-green-100 text-green-800';
      case PaymentVerificationStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800';
      case PaymentVerificationStatus.REQUIRES_ACTION:
        return 'bg-blue-100 text-blue-800';
      case PaymentVerificationStatus.FAILED:
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return <div className="p-4 text-center">Loading payment history...</div>;
  }

  if (paymentRecords.length === 0) {
    return (
      <div className="p-4 text-center">
        {showTitle && <h2 className="text-xl font-semibold mb-4">Payment History</h2>}
        <p className="text-gray-600">No payment records found.</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden">
      {showTitle && (
        <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-800">Payment History</h2>
        </div>
      )}

      <div className="divide-y divide-gray-200">
        {paymentRecords.map((record, index) => (
          <div key={index} className="p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <span className="text-sm font-medium text-gray-500">Payment ID</span>
                <p className="font-mono text-sm">{record.paymentIntentId}</p>
              </div>
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                {record.status}
              </span>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <span className="text-sm font-medium text-gray-500">Amount</span>
                <p className="text-lg font-semibold">${(record.amount / 100).toFixed(2)}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Date</span>
                <p>{formatDate(record.createdAt)}</p>
              </div>
            </div>

            {record.metadata && (
              <div className="mt-4">
                <span className="text-sm font-medium text-gray-500">Details</span>
                <div className="mt-1 text-sm text-gray-600">
                  {record.metadata.testMode && (
                    <div className="flex items-center space-x-1">
                      <span className="inline-block w-2 h-2 bg-purple-400 rounded-full"></span>
                      <span>Test Mode</span>
                    </div>
                  )}
                  {record.metadata.paymentMethod && (
                    <p>Method: {Array.isArray(record.metadata.paymentMethod) ? record.metadata.paymentMethod.join(', ') : record.metadata.paymentMethod}</p>
                  )}
                  {record.metadata.currency && (
                    <p>Currency: {record.metadata.currency.toUpperCase()}</p>
                  )}
                </div>
              </div>
            )}

            <div className="mt-4">
              <a 
                href={`https://dashboard.stripe.com/test/payments/${record.paymentIntentId}`}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800"
              >
                View in Stripe Dashboard <ExternalLink className="h-3 w-3 ml-1" />
              </a>
            </div>
          </div>
        ))}
      </div>

      <div className="px-6 py-4 bg-gray-50 text-xs text-gray-500">
        <p>Payment records are stored locally for demonstration purposes.</p>
        <p>In a production environment, these would be securely stored in a database.</p>
      </div>
    </div>
  );
}