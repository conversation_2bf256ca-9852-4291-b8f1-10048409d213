import { Link } from 'react-router-dom';
import { Mail, Phone, Info, Github } from 'lucide-react';

export function AdminFooter() {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-green-900 text-white mt-auto py-6 w-full">
      <div className="max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <img
                src="/logo.jpg"
                alt="jaisalgoonline"
                className="h-12 w-auto object-contain"
              />
              <span className="text-xl font-bold">jaisalgoonline Admin</span>
            </div>
            <p className="text-gray-300 leading-relaxed">
              Admin dashboard for managing products, orders, and users.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-3">
              <li><Link to="/admin" className="text-gray-400 hover:text-white transition-colors">Dashboard</Link></li>
              <li><Link to="/admin/products" className="text-gray-400 hover:text-white transition-colors">Products</Link></li>
              <li><Link to="/admin/orders" className="text-gray-400 hover:text-white transition-colors">Orders</Link></li>
              <li><Link to="/admin/users" className="text-gray-400 hover:text-white transition-colors">Users</Link></li>
            </ul>
          </div>

          {/* System Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">System Info</h3>
            <ul className="space-y-3">
              <li className="flex items-center text-gray-400">
                <Info className="h-4 w-4 mr-2 text-green-400" />
                <span>Version 1.0.0</span>
              </li>
              <li><Link to="/" className="text-gray-400 hover:text-white transition-colors">View Store</Link></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Documentation</a></li>
              <li><a href="#" className="text-gray-400 hover:text-white transition-colors flex items-center">
                <Github className="h-4 w-4 mr-1" />
                <span>GitHub</span>
              </a></li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Contact Us</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-green-400" />
                <span className="text-gray-400"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-green-400" />
                <span className="text-gray-400">+****************</span>
              </div>
            </div>
          </div>
        </div>
        
        <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-400 text-sm">
            © {currentYear} jaisalgoonline. All rights reserved. Admin Dashboard.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link to="/privacy-policy" className="text-gray-400 hover:text-white text-sm transition-colors">Privacy Policy</Link>
            <Link to="/terms-of-service" className="text-gray-400 hover:text-white text-sm transition-colors">Terms of Service</Link>
            <Link to="/" className="text-gray-400 hover:text-white text-sm transition-colors">Return to Store</Link>
          </div>
        </div>
      </div>
    </footer>
  );
}

export default AdminFooter;