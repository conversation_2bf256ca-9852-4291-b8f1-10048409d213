const { spawn } = require('child_process');
const path = require('path');
const os = require('os');

// Determine if we're on Windows
const isWindows = os.platform() === 'win32';
const npmCmd = isWindows ? 'npm.cmd' : 'npm';

// Start the server
console.log('Starting server...');
const serverProcess = spawn(npmCmd, ['start'], {
  cwd: path.join(__dirname, 'server'),
  stdio: 'inherit',
});

// Start the client after a short delay to ensure server starts first
setTimeout(() => {
  console.log('Starting client...');
  const clientProcess = spawn(npmCmd, ['start'], {
    cwd: __dirname,
    stdio: 'inherit',
  });

  clientProcess.on('close', (code) => {
    console.log(`Client process exited with code ${code}`);
    serverProcess.kill();
  });
}, 3000);

// Handle process termination
process.on('SIGINT', () => {
  console.log('Stopping all processes...');
  serverProcess.kill();
  process.exit();
});

serverProcess.on('close', (code) => {
  console.log(`Server process exited with code ${code}`);
  process.exit();
});