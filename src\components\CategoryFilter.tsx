interface CategoryFilterProps {
  categories: string[];
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
}

export function CategoryFilter({ categories, selectedCategory, onCategoryChange }: CategoryFilterProps) {
  return (
    <div className="flex flex-wrap gap-4 justify-center">
      {categories.map((category, index) => (
        <button
          key={category}
          onClick={() => onCategoryChange(category)}
          className={`px-6 py-3 rounded-2xl text-sm font-semibold transition-all duration-300 transform hover:scale-105 animate-fade-in ${
            selectedCategory === category
              ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-green'
              : 'bg-white text-gray-700 hover:bg-green-50 hover:text-green-600 shadow-md hover:shadow-lg border border-gray-200'
          }`}
          style={{ animationDelay: `${index * 0.1}s` }}
        >
          {category}
        </button>
      ))}
    </div>
  );
}