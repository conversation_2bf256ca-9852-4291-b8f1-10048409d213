/**
 * Environment variable utility for consistent access to environment variables
 * throughout the application.
 */

// For client-side environment variables in Vite, they must be prefixed with VITE_
// See: https://vitejs.dev/guide/env-and-mode.html

// Stripe publishable key
export const STRIPE_PUBLISHABLE_KEY = import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || 
  process.env.REACT_APP_STRIPE_PUBLISHABLE_KEY || '';

// API URL
export const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:4000';

// Environment
export const IS_DEVELOPMENT = import.meta.env.DEV || process.env.NODE_ENV === 'development';
export const IS_PRODUCTION = import.meta.env.PROD || process.env.NODE_ENV === 'production';

/**
 * Get an environment variable with fallbacks
 * @param key The environment variable key
 * @param defaultValue Default value if not found
 * @returns The environment variable value or default
 */
export function getEnv(key: string, defaultValue: string = ''): string {
  return import.meta.env[`VITE_${key}`] || 
         process.env[`REACT_APP_${key}`] || 
         process.env[key] || 
         defaultValue;
}