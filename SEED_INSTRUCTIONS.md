# 🌱 Database Seeding Instructions

## Quick Start

1. **Make sure your MongoDB is running**
2. **Run the seeding command:**
   ```bash
   npm run seed
   ```

## What You'll Get

### 👤 Admin User Created
- **Email**: `<EMAIL>`
- **Password**: `admin123456`
- **Role**: Admin (full access to admin panel)

### 📦 28 Products Added
- **6 Featured Products** (displayed in featured section)
- **22 Regular Products** (displayed in complete collection)
- **All categories populated**: Electronics, Home & Kitchen, Photography, etc.
- **External images from Unsplash** (no alt text issues)
- **Realistic data**: prices, ratings, reviews, inventory

## Featured Products Include:
- Premium Wireless Headphones (SALE)
- Smart Fitness Watch
- Professional Camera Lens (SALE)
- Gaming Mechanical Keyboard (SALE)
- Wireless Earbuds Pro
- Kitchen Knife Set

## After Seeding

1. **Login to Admin Panel:**
   - Go to `/admin`
   - Use the admin credentials above
   - Manage all products, users, and orders

2. **Check Your Homepage:**
   - Featured products will appear in the featured section
   - All 28 products will be available in the shop
   - Categories will be properly populated

3. **Test the System:**
   - Browse products by category
   - Add products to cart
   - Test the review system
   - Use the admin panel to manage inventory

## Important Notes

⚠️ **This will DELETE all existing users and products**
✅ **All images are external URLs (no storage issues)**
✅ **Products have realistic ratings and review counts**
✅ **Inventory quantities are set for each product**

## Troubleshooting

If you get errors:
1. Make sure MongoDB is running
2. Check your `.env` file has correct `MONGO_URI`
3. Ensure you have internet connection (for external images)

## Success Output

You should see:
```
MongoDB Connected...
Clearing existing data...
Creating admin user...
Admin user created successfully!
Admin credentials:
Email: <EMAIL>
Password: admin123456
Creating products...
28 products created successfully!
Database seeding completed!
```

Now your e-commerce site is fully populated and ready to use! 🎉
