/**
 * Payment routes for Stripe integration
 */
const express = require('express');
const router = express.Router();

// Initialize Stripe with the secret key
const stripeKey = process.env.VITE_STRIPE_SECRET_KEY || process.env.REACT_APP_STRIPE_SECRET_KEY;
const stripe = require('stripe')(stripeKey);

/**
 * Create a payment intent
 */
router.post('/create-payment-intent', async (req, res) => {
  try {
    const { amount, currency = 'usd' } = req.body;
    
    if (!amount) {
      return res.status(400).json({ error: 'Amount is required' });
    }

    // Ensure amount is an integer (Stripe requires integer amounts in cents)
    // Use parseInt to ensure we have a clean integer without any decimal part
    const amountInCents = parseInt(Math.round(Number(amount)), 10);
    
    if (isNaN(amountInCents) || amountInCents <= 0) {
      return res.status(400).json({ error: 'Invalid amount value' });
    }

    console.log('Creating payment intent with amount:', amountInCents);

    // Create a PaymentIntent with the order amount and currency
    const paymentIntent = await stripe.paymentIntents.create({
      amount: amountInCents,
      currency,
      // In a production app, you would include more parameters like:
      // receipt_email, customer, shipping, etc.
    });

    // Send the client secret to the client
    res.status(200).json({
      clientSecret: paymentIntent.client_secret,
      paymentIntentId: paymentIntent.id // Include the payment intent ID
    });
  } catch (error) {
    console.error('Error creating payment intent:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Verify a payment intent
 */
router.get('/verify-payment-intent/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({ error: 'Payment intent ID is required' });
    }

    // Retrieve the payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(id);

    // Map Stripe status to our status enum
    let status;
    switch (paymentIntent.status) {
      case 'succeeded':
        status = 'succeeded';
        break;
      case 'requires_action':
      case 'requires_confirmation':
      case 'requires_capture':
        status = 'requires_action';
        break;
      case 'processing':
        status = 'pending';
        break;
      case 'canceled':
      case 'requires_payment_method':
        status = 'failed';
        break;
      default:
        status = 'pending';
    }

    // Return the payment record
    res.status(200).json({
      paymentIntentId: paymentIntent.id,
      amount: paymentIntent.amount,
      status,
      createdAt: new Date(paymentIntent.created * 1000), // Convert Unix timestamp to Date
      metadata: {
        paymentMethod: paymentIntent.payment_method_types,
        currency: paymentIntent.currency,
        testMode: paymentIntent.livemode === false,
        description: paymentIntent.description || null
      }
    });
  } catch (error) {
    console.error('Error verifying payment intent:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * Get payment receipt URL
 */
router.get('/payment-receipt/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    if (!id) {
      return res.status(400).json({ error: 'Payment intent ID is required' });
    }

    // Retrieve the payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(id);
    
    // Get the charge ID from the payment intent
    const chargeId = paymentIntent.latest_charge;
    
    if (!chargeId) {
      return res.status(404).json({ error: 'No charge found for this payment intent' });
    }
    
    // Retrieve the charge to get the receipt URL
    const charge = await stripe.charges.retrieve(chargeId);
    
    if (!charge.receipt_url) {
      return res.status(404).json({ error: 'No receipt available for this payment' });
    }
    
    // Return the receipt URL
    res.status(200).json({
      receiptUrl: charge.receipt_url
    });
  } catch (error) {
    console.error('Error getting payment receipt:', error);
    res.status(500).json({ error: error.message });
  }
});

module.exports = router;