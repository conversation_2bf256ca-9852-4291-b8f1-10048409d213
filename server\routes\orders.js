const express = require('express');
const router = express.Router();
const Order = require('../models/Order');
const Product = require('../models/Product');
const Stats = require('../models/Stats');
const { protect, admin } = require('../middleware/auth');

/**
 * @route   POST /api/orders
 * @desc    Create a new order
 * @access  Private
 */
router.post('/', protect, async (req, res) => {
  try {
    const { 
      items, 
      shippingAddress, 
      paymentInfo,
      subtotal,
      tax,
      shipping,
      total
    } = req.body;

    if (!items || items.length === 0) {
      return res.status(400).json({ error: 'No order items' });
    }

    // Create order
    const order = new Order({
      user: req.user._id,
      items,
      shippingAddress,
      paymentInfo,
      subtotal,
      tax,
      shipping,
      total
    });

    const createdOrder = await order.save();

    // Update stats
    await updateStats(createdOrder);

    res.status(201).json(createdOrder);
  } catch (error) {
    console.error('Error creating order:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/orders/myorders
 * @desc    Get logged in user orders
 * @access  Private
 */
router.get('/myorders', protect, async (req, res) => {
  try {
    const orders = await Order.find({ user: req.user._id })
      .sort({ createdAt: -1 });
    res.json(orders);
  } catch (error) {
    console.error('Error fetching user orders:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/orders/:id
 * @desc    Get order by ID
 * @access  Private
 */
router.get('/:id', protect, async (req, res) => {
  try {
    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({ error: 'Order not found' });
    }

    // Check if the order belongs to the logged in user or if user is admin
    if (order.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Not authorized to view this order' });
    }

    res.json(order);
  } catch (error) {
    console.error('Error fetching order:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   PUT /api/orders/:id/pay
 * @desc    Update order to paid
 * @access  Private
 */
router.put('/:id/pay', protect, async (req, res) => {
  try {
    const order = await Order.findById(req.params.id);

    if (!order) {
      return res.status(404).json({ error: 'Order not found' });
    }

    // Check if the order belongs to the logged in user or if user is admin
    if (order.user.toString() !== req.user._id.toString() && req.user.role !== 'admin') {
      return res.status(403).json({ error: 'Not authorized to update this order' });
    }

    // Update payment info
    order.paymentInfo = {
      ...order.paymentInfo,
      ...req.body,
      paymentStatus: 'succeeded'
    };

    const updatedOrder = await order.save();
    res.json(updatedOrder);
  } catch (error) {
    console.error('Error updating order payment:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/orders
 * @desc    Get all orders
 * @access  Private/Admin
 */
router.get('/', protect, admin, async (req, res) => {
  try {
    const { limit = 20, page = 1, status } = req.query;
    const query = {};
    
    // Filter by status
    if (status) {
      query.status = status;
    }
    
    // Pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const orders = await Order.find(query)
      .populate('user', 'firstName lastName email')
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .skip(skip);
    
    const total = await Order.countDocuments(query);
    
    res.json({
      orders,
      page: parseInt(page),
      pages: Math.ceil(total / parseInt(limit)),
      total
    });
  } catch (error) {
    console.error('Error fetching all orders:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   PUT /api/orders/:id/status
 * @desc    Update order status
 * @access  Private/Admin
 */
router.put('/:id/status', protect, admin, async (req, res) => {
  try {
    const { status } = req.body;
    
    if (!status) {
      return res.status(400).json({ error: 'Status is required' });
    }
    
    const order = await Order.findById(req.params.id);
    
    if (!order) {
      return res.status(404).json({ error: 'Order not found' });
    }
    
    order.status = status;
    const updatedOrder = await order.save();
    
    res.json(updatedOrder);
  } catch (error) {
    console.error('Error updating order status:', error);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   DELETE /api/orders/:id
 * @desc    Delete an order
 * @access  Private/Admin
 */
router.delete('/:id', protect, admin, async (req, res) => {
  try {
    const order = await Order.findById(req.params.id);
    
    if (!order) {
      return res.status(404).json({ error: 'Order not found' });
    }
    
    await order.deleteOne();
    res.json({ message: 'Order removed' });
  } catch (error) {
    console.error('Error deleting order:', error);
    res.status(500).json({ error: error.message });
  }
});

// Helper function to update stats when a new order is created
async function updateStats(order) {
  try {
    // Get or create stats document
    let stats = await Stats.findOne({});
    if (!stats) {
      stats = new Stats();
    }
    
    // Update total orders and revenue
    stats.totalOrders += 1;
    stats.totalRevenue += order.total;
    
    // Update daily revenue
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const dailyRevenueIndex = stats.dailyRevenue.findIndex(
      day => new Date(day.date).toDateString() === today.toDateString()
    );
    
    if (dailyRevenueIndex >= 0) {
      stats.dailyRevenue[dailyRevenueIndex].amount += order.total;
    } else {
      stats.dailyRevenue.push({
        date: today,
        amount: order.total
      });
    }
    
    // Update monthly sales
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();
    
    const monthlySalesIndex = stats.monthlySales.findIndex(
      month => month.month === currentMonth && month.year === currentYear
    );
    
    if (monthlySalesIndex >= 0) {
      stats.monthlySales[monthlySalesIndex].amount += order.total;
      stats.monthlySales[monthlySalesIndex].orders += 1;
    } else {
      stats.monthlySales.push({
        month: currentMonth,
        year: currentYear,
        amount: order.total,
        orders: 1
      });
    }
    
    // Update top selling products
    for (const item of order.items) {
      const productIndex = stats.topSellingProducts.findIndex(
        product => product.product.toString() === item.product.toString()
      );
      
      if (productIndex >= 0) {
        stats.topSellingProducts[productIndex].quantity += item.quantity;
        stats.topSellingProducts[productIndex].revenue += item.price * item.quantity;
      } else {
        stats.topSellingProducts.push({
          product: item.product,
          quantity: item.quantity,
          revenue: item.price * item.quantity
        });
      }
    }
    
    // Update top categories
    for (const item of order.items) {
      // Get product to find its category
      const product = await Product.findById(item.product);
      if (product) {
        const categoryIndex = stats.topCategories.findIndex(
          cat => cat.category === product.category
        );
        
        if (categoryIndex >= 0) {
          stats.topCategories[categoryIndex].sales += item.price * item.quantity;
        } else {
          stats.topCategories.push({
            category: product.category,
            sales: item.price * item.quantity
          });
        }
      }
    }
    
    // Sort top selling products and categories
    stats.topSellingProducts.sort((a, b) => b.revenue - a.revenue);
    stats.topCategories.sort((a, b) => b.sales - a.sales);
    
    // Limit arrays to top 10
    stats.topSellingProducts = stats.topSellingProducts.slice(0, 10);
    stats.topCategories = stats.topCategories.slice(0, 10);
    
    await stats.save();
  } catch (error) {
    console.error('Error updating stats:', error);
  }
}

module.exports = router;