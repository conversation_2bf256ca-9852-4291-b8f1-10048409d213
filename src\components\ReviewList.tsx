import { Star } from 'lucide-react';
import { Review } from '../types';

interface ReviewListProps {
  reviews: Review[];
  loading?: boolean;
}

export default function ReviewList({ reviews, loading = false }: ReviewListProps) {
  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="bg-green-50 p-6 rounded-xl border border-green-100">
            <div className="h-4 bg-green-200 rounded w-1/4 mb-2"></div>
            <div className="h-3 bg-green-200 rounded w-1/2 mb-4"></div>
            <div className="h-3 bg-green-200 rounded w-full mb-2"></div>
            <div className="h-3 bg-green-200 rounded w-full mb-2"></div>
            <div className="h-3 bg-green-200 rounded w-3/4"></div>
          </div>
        ))}
      </div>
    );
  }

  if (reviews.length === 0) {
    return (
      <div className="text-center py-12 bg-green-50 rounded-xl border border-green-100">
        <div className="text-green-400 mb-4">
          <Star className="mx-auto h-16 w-16" />
        </div>
        <p className="text-green-700 font-medium text-lg">No reviews yet. Be the first to review this product!</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {reviews.map((review) => (
        <div key={review._id || review.id} className="bg-white p-6 rounded-xl shadow-md border border-green-100 hover:shadow-lg transition-shadow duration-300">
          <div className="flex justify-between items-start mb-3">
            <h3 className="font-bold text-gray-900 text-lg">{review.title}</h3>
            <span className="text-sm text-green-600 bg-green-100 px-3 py-1 rounded-full font-medium">
              {new Date(review.createdAt).toLocaleDateString()}
            </span>
          </div>

          <div className="flex items-center mb-3">
            <div className="flex items-center space-x-1">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`h-5 w-5 ${i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
                />
              ))}
            </div>
            <span className="ml-3 text-sm text-green-700 bg-green-50 px-2 py-1 rounded-full font-semibold">{review.rating.toFixed(1)} out of 5</span>
          </div>

          <div className="text-sm text-green-600 mb-3">
            <span className="font-semibold">By {review.userName || 'Anonymous'}</span>
          </div>

          <p className="text-gray-700 leading-relaxed">{review.comment}</p>
        </div>
      ))}
    </div>
  );
}