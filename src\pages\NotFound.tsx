import React from 'react';
import { Link } from 'react-router-dom';
import { Home, ArrowLeft, Search } from 'lucide-react';

export function NotFound() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full text-center">
        <div className="mb-8">
          <div className="mx-auto w-24 h-24 bg-brand-primary rounded-full flex items-center justify-center mb-6">
            <span className="text-white font-bold text-2xl">404</span>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Page Not Found</h1>
          <p className="text-lg text-gray-600 mb-8">
            Sorry, we couldn't find the page you're looking for. It might have been moved, deleted, or you entered the wrong URL.
          </p>
        </div>

        <div className="space-y-4">
          <Link
            to="/"
            className="w-full bg-brand-primary text-white px-6 py-3 rounded-full font-semibold hover:bg-brand-primary-hover transition-colors flex items-center justify-center space-x-2 group"
          >
            <Home className="h-5 w-5" />
            <span>Go Home</span>
          </Link>

          <Link
            to="/shop"
            className="w-full border-2 border-brand-primary text-brand-primary px-6 py-3 rounded-full font-semibold hover:bg-brand-primary hover:text-white transition-colors flex items-center justify-center space-x-2 group"
          >
            <Search className="h-5 w-5" />
            <span>Browse Products</span>
          </Link>
          
          <button
            onClick={() => window.history.back()}
            className="w-full text-gray-600 hover:text-gray-900 px-6 py-3 rounded-full font-medium transition-colors flex items-center justify-center space-x-2"
          >
            <ArrowLeft className="h-5 w-5" />
            <span>Go Back</span>
          </button>
        </div>

        <div className="mt-12 text-sm text-gray-500">
          <p>If you believe this is an error, please contact our support team.</p>
        </div>
      </div>
    </div>
  );
}
