# Database Seeding Script

This script populates your database with sample products and creates an admin user.

## What it creates:

### Admin User
- **Email**: <EMAIL>
- **Password**: admin123456
- **Role**: admin

### Products
- 28 total products across various categories
- 6 featured products
- High-quality external images from Unsplash
- Realistic pricing and inventory data
- Customer ratings and review counts

## Categories included:
- Electronics
- Home & Kitchen
- Home & Office
- Photography
- Lifestyle
- Home & Decor
- Fitness
- Accessories

## How to run:

1. Make sure your MongoDB is running
2. Ensure your `.env` file has the correct `MONGO_URI`
3. Run from the project root:

```bash
npm run seed
```

## What the script does:
1. Connects to your MongoDB database
2. Clears existing users and products
3. Creates the admin user with hashed password
4. Creates all 28 products with external images
5. Displays success messages and admin credentials

## Important Notes:
- This script will **DELETE ALL EXISTING** users and products
- Make sure to backup your data if you have important information
- The admin password is displayed in the console after creation
- All product images are hosted externally on Unsplash (no alt text issues)

## Troubleshooting:
- Ensure MongoDB is running
- Check your MONGO_URI in the .env file
- Make sure you have internet connection for external images
- Verify that the server models are accessible from the script location
